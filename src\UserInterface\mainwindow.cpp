#include "mainwindow.h"

#include "ui_mainwindow.h"
#include "liveviewwidget.h"
#include "scenemanager.h"

#include <QAction>
#include <QCoreApplication>

#include "spdlog/sinks/stdout_color_sinks.h"
#include <iostream>

MainWindow::MainWindow(QWidget *parent)
    : QMainWindow(parent)
    , ui(new Ui::MainWindow)
{
    ui->setupUi(this);
    m_logger = spdlog::get("UserInterfaceApplication");
    if (!m_logger) {
        m_logger = spdlog::stdout_color_mt("UserInterfaceApplication");
        m_logger->set_level(spdlog::level::debug);
    }
    initialize();
}

MainWindow::~MainWindow()
{
    delete ui;
    if (!m_videoStreamWindow.isNull()) {
        m_videoStreamWindow->disconnect(this);
        delete m_videoStreamWindow;
    }
}

void MainWindow::initialize()
{
    resize(1600, 1000);
    connect(ui->actionOpenVideoStream, &QAction::triggered, this, &MainWindow::showVideoStreamWindow);
    connect(ui->actionOpenSelectFilterList, &QAction::triggered, this, &MainWindow::openSelectFilterList);
    connect(ui->actionQuit, &QAction::triggered,
            QCoreApplication::instance(), &QCoreApplication::quit,
            Qt::QueuedConnection);
}

void MainWindow::showVideoStreamWindow()
{
    if (m_videoStreamWindow.isNull()) {
        m_videoStreamWindow = new VideoStreamWindow();
        m_videoStreamWindow->connect(m_videoStreamWindow, &VideoStreamWindow::videoStreamWindowAboutToGetClosed,
                                     this, [this]() {
            ui->actionOpenVideoStream->setEnabled(true);
            emit this->videoStreamStopRequested();
        });
    }

    if (m_videoStreamWindow->isVisible()) {
        m_logger->info("Video Stream Window already visible");
        return;
    }

    ui->actionOpenVideoStream->setEnabled(false);
    m_videoStreamWindow->show();
    m_videoStreamWindow->raise();
    m_videoStreamWindow->activateWindow();
    emit videoStreamStartRequested();
}

void  MainWindow::openSelectFilterList()
{
    if (m_filterListWidget.isNull()) {
        auto sceneManager = ui->centralWidget->sceneManager();
        if (sceneManager == nullptr) {
            m_logger->info("No SceneManager set: Cannot open filter list");
            return;
        }
        auto model = sceneManager->filterListModel();
        m_filterListWidget = new SelectFilterWidget(model);
        m_filterListWidget->connect(m_filterListWidget, &SelectFilterWidget::filterWidgetAboutToGetClosed,
                                     this, [this]() {
                                         ui->actionOpenSelectFilterList->setEnabled(true);
                                     });
    }

    if (m_filterListWidget->isVisible()) {
        m_logger->info("Filter widget already visible");
        return;
    }

    ui->actionOpenSelectFilterList->setEnabled(false);
    m_filterListWidget->show();
    m_filterListWidget->raise();
    m_filterListWidget->activateWindow();
}


void MainWindow::setSceneManager(SceneManager *sceneManager)
{
    ui->centralWidget->setSceneManager(sceneManager);
}

void MainWindow::updateVideoFrame(const QImage& frame, size_t frameId)
{
    if (!m_videoStreamWindow->isVisible()) {
        m_logger->warn("Video Stream Window is not active: Cannot update video stream!");
    }

    m_logger->info("Update Video Stream called");
    m_videoStreamWindow->updateFrameImage(frame, frameId);
}
