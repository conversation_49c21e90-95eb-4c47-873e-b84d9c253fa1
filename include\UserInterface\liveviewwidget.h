#ifndef LIVE_VIEW_WIDGET_H
#define LIVE_VIEW_WIDGET_H

#include <QWidget>
#include <QPointer>

#include "spdlog/spdlog.h"

class SceneManager;

QT_BEGIN_NAMESPACE
namespace Ui {
class LiveViewWidget;
}
QT_END_NAMESPACE

class LiveViewWidget : public QWidget
{
    Q_OBJECT

public:
    explicit LiveViewWidget(QWidget *parent = nullptr);
    ~LiveViewWidget();

    void setSceneManager(SceneManager *sceneManager);
    SceneManager* sceneManager() const;

private:
    void initializeUI();

    Ui::LiveViewWidget *ui;

    QPointer<SceneManager> m_sceneManager;
    std::shared_ptr<spdlog::logger> m_logger;

};
#endif // LIVE_VIEW_WIDGET_H
