#ifndef COMMON_H
#define COMMON_H

#include <cstdint>

/**
 * @brief Structure to hold image information and share it across different modules
 * 
 * This structure is used to pass image information between different modules
 * in the system. It contains the GPU pointer, width, height, number of channels,
 * type, and step (pitch) of the image.
 * 
 * @note The step (pitch) is the number of bytes between the start of one row and the start of the next row.
 * @note The type is the OpenCV type of the image. It can be obtained using the CV_8UC3, CV_8UC4, etc. macros.
 * 
 * @param gpuPtr Pointer to the GPU memory containing the image data
 * @param width Width of the image in pixels
 * @param height Height of the image in pixels
 * @param channels Number of channels in the image (e.g., 3 for RGB, 4 for RGBA)
 * @param type OpenCV type of the image
 * @param step Number of bytes between the start of one row and the start of the next row
 */
struct ImageInfoGPU
{
    uint8_t* gpuPtr;
    int width;
    int height;
    int channels;
    int type;
    int step;
};

#endif // COMMON_H