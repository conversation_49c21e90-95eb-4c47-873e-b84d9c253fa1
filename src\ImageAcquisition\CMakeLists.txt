# ------------- Mo<PERSON><PERSON> (ImageAcquisition) -------------
add_library(ImageAcquisition STATIC)

# List all the sources in this directory
file(GLOB_RECURSE IA_SOURCES CONFIGURE_DEPENDS "*.cpp")

target_sources(ImageAcquisition 
    PRIVATE
        ${IA_SOURCES}
)

# Compute EURESYS_DIR from environment variable if available
if(DEFINED ENV{EURESYS_COAXLINK_GENTL64_CTI})
    get_filename_component(EURESYS_GENTL_DIR "$ENV{EURESYS_COAXLINK_GENTL64_CTI}" DIRECTORY)
    get_filename_component(EURESYS_CTI_DIR "${EURESYS_GENTL_DIR}" DIRECTORY)
    get_filename_component(EURESYS_BASE_DIR "${EURESYS_CTI_DIR}" DIRECTORY)
    set(EURESYS_DIR "${EURESYS_BASE_DIR}/include" CACHE STRING "The Euresys include folder")
else()
    message(WARNING "EURESYS_COAXLINK_GENTL64_CTI not defined. Please reboot the computer after Euresys driver installation.")
endif()

# Enable CUDA
enable_language(CUDA)
set(CMAKE_CUDA_STANDARD 17)
set(CMAKE_CUDA_STANDARD_REQUIRED ON)

set_target_properties(ImageAcquisition
    PROPERTIES
        CUDA_ARCHITECTURES "89"
        CUDA_SEPARABLE_COMPILATION ON
        POSITION_INDEPENDENT_CODE ON
        CUDA_RESOLVE_DEVICE_SYMBOLS ON
        LINKER_LANGUAGE CUDA
        WINDOWS_EXPORT_ALL_SYMBOLS ON
        AUTOMOC OFF
)

target_include_directories(ImageAcquisition 
    PUBLIC
        $<BUILD_INTERFACE:${CMAKE_SOURCE_DIR}/include>
    PRIVATE
        $<BUILD_INTERFACE:${CMAKE_SOURCE_DIR}/include/ImageAcquisition>
    # Keep the SYSTEM keyword for GCC/Clang
    SYSTEM PRIVATE
        $<BUILD_INTERFACE:${EURESYS_DIR}>       
)

# --- MSVC: suppress warnings that originate from Euresys headers
if (MSVC)
    target_compile_options(ImageAcquisition PRIVATE
        /external:W0                      # turn warnings in external headers off
        /external:I "${EURESYS_DIR}"      # mark this include path as external
    )
endif()

# Let downstream targets know what to link if they use "ImageAcquisition"
target_link_libraries(ImageAcquisition
    PUBLIC
        ${CUDA_RUNTIME_LIBRARY}
        cuda
        cudart
        spdlog::spdlog # TODO: Remove if root CMakeLists.txt links to spdlog
)