#include "scenemanager.h"

#include "rollscene.h"

#include "spdlog/sinks/stdout_color_sinks.h"
#include <QBrush>
#include <QGraphicsEllipseItem>
#include <QPen>

SceneManager::SceneManager(QObject* parent)
    : QObject(parent)
    , m_filterListModel(new FilterListModel(this))
{
    m_logger = spdlog::get("UserInterfaceApplication");
    if (!m_logger) {
        m_logger = spdlog::stdout_color_mt("UserInterfaceApplication");
        m_logger->set_level(spdlog::level::debug);
    }
    m_logger->info("Scene Manager created ...");
}

void SceneManager::initializeSceneForCamera(CameraPosition position,const QRectF& viewport)
{
    auto& parameters = (position == CameraPosition::TopCameraView) ? m_topScene : m_bottomScene;

    if (parameters.isInitialized) {
        m_logger->warn("SceneManager already initialized for camera position {}", static_cast<int>(position));
        // ToDo: Reset scene manager for camera position
        return;
    }

    if (viewport.isValid()) {
        parameters.initialViewport = viewport;
    } else {
        parameters.initialViewport = QRectF(0, 0, 2000, 2000);
    }
    parameters.viewport = parameters.initialViewport;
    // Consider the unlikely case that the roll has been moved already
    parameters.viewport.translate(0.0, m_totalForwardMovement);
    parameters.scene = new RollScene(this);

    parameters.model.reset(new DetectionModel(this));
    parameters.scene->setDetectionModel(parameters.model);
    parameters.scene->setFilterListModel(m_filterListModel);

    connect(parameters.model.data(), &QAbstractItemModel::rowsInserted,
        this, [position, this](const QModelIndex&, int first, int last) {
            for (auto idx = first; idx <= last; idx++) {
                this->updateSubRollAggregations(position, idx);
            }
        });

    parameters.isInitialized = true;
}

RollScene* SceneManager::sceneForCamera(CameraPosition position)
{
    if (!isInitialized(position)) {
        return nullptr;
    }

    if (position == CameraPosition::TopCameraView) {
        return m_topScene.scene;
    } else if (position == CameraPosition::BottomCameraView) {
        return m_bottomScene.scene;
    }
    m_logger->info("Camera view not implemented for position {}", static_cast<int>(position));
    return nullptr;
}

bool SceneManager::isInitialized(CameraPosition position) const
{
    if (position == CameraPosition::TopCameraView) {
        return m_topScene.isInitialized;
    } else if (position == CameraPosition::BottomCameraView) {
        return m_bottomScene.isInitialized;
    }
    return false;
}

QRectF SceneManager::cameraViewport(CameraPosition position) const
{
    if (!isInitialized(position)) {
        return QRectF();
    }

    if (position == CameraPosition::TopCameraView) {
        return m_topScene.viewport;
    } else if (position == CameraPosition::BottomCameraView) {
        return m_bottomScene.viewport;
    }
    m_logger->info("Camera view not implemented for position {}", static_cast<int>(position));
    return QRectF();
}

QSharedPointer<DetectionModel> SceneManager::detectionModel(CameraPosition position) const
{
    if (!isInitialized(position)) {
        return nullptr;
    }

    if (position == CameraPosition::TopCameraView) {
        return m_topScene.model;
    } else if (position == CameraPosition::BottomCameraView) {
        return m_bottomScene.model;
    }
    m_logger->info("Camera view not implemented for position {}", static_cast<int>(position));
    return nullptr;
}

void SceneManager::SceneManager::setRollConfiguration(const RollConfiguration& configuration)
{
    if (configuration.dimensions != m_rollDimensions) {
        if (configuration.dimensions.isValid()) {
            m_rollDimensions = configuration.dimensions;
            emit rollDimensionsChanged();
        } else {
            m_logger->warn("Dimensions are not valid");
            return;
        }
    }

    for (const SceneParameters& sceneParameters : initializedScenes()) {
        auto scene = sceneParameters.scene;
        scene->setSceneRect(configuration.dimensions);
        // debug rects

        for (auto i = 0; i < 10; i++) {
            auto x = i * 20.0;
            scene->addRect(QRectF(x, 0.0, 10.0, 50.0), QPen(Qt::red));
        }

        for (auto i = 0; i < 10; i++) {
            auto x = (m_rollDimensions.right() - i * 20.0) - 15.0;
            scene->addRect(QRectF(x, m_rollDimensions.bottom() - 10.0, 10.0, 10.0), QPen(Qt::red));
        }
    }
    setNumberOfSubRolls(configuration.numberOfSubRolls, configuration.finalProductWidth, configuration.startId);
    emit rollConfigurationChanged();
}

QRectF SceneManager::rollDimensions() const
{
    return m_rollDimensions;
}

qreal SceneManager::leftBorderWidth() const
{
    return m_leftBorderWidth;
}

qreal SceneManager::rightBorderWidth() const
{
    return m_rightBorderWidth;
}

FilterListModel*  SceneManager::filterListModel() const
{
    return m_filterListModel.data();
}

void SceneManager::setNumberOfSubRolls(int numberOfSubRolls, qreal finalProductWidth, int startId)
{

    m_subRolls.clear();

    quint32 subRollWidth = finalProductWidth / numberOfSubRolls;
    qreal borderWidth = (m_rollDimensions.width() - finalProductWidth) * 0.5;
    m_subRolls.reserve(numberOfSubRolls);

    m_leftBorderWidth = borderWidth;
    m_rightBorderWidth = borderWidth;

    // sub roll ids decreases from left to right
    int currentId = startId + numberOfSubRolls - 1;
    qreal position = borderWidth;
    for (auto i = 0; i < numberOfSubRolls; i++) {
        SubRoll subRoll = { currentId, position, 0, 0 };
        m_subRolls.push_back(subRoll);
        currentId--;
        position += subRollWidth;
    }

    for (const SceneParameters& sceneParameters : initializedScenes()) {
        auto scene = sceneParameters.scene;
        for (auto& subRoll : std::as_const(m_subRolls)) {
            scene->setSceneConfigurationItem(RollScene::SubRollItem, subRoll.startPosition);
        }
        scene->setSceneConfigurationItem(RollScene::LeftBorderItem, 0.0, borderWidth);
        scene->setSceneConfigurationItem(RollScene::RightBorderItem, m_rollDimensions.right() - borderWidth, borderWidth);
        scene->setSceneConfigurationItem(RollScene::InspectorLine, sceneParameters.viewport.center().y());
    }
}

void SceneManager::updateSubRollAggregations(CameraPosition cameraPosition, int detectionRowNr)
{
    QSharedPointer<DetectionModel> model = nullptr;
    if (cameraPosition == CameraPosition::TopCameraView) {
        model = m_topScene.model;
    } else if (cameraPosition == CameraPosition::BottomCameraView) {
        model = m_bottomScene.model;
    }

    if (model.isNull()) {
        m_logger->warn("Cannot update sub roll aggregations for camera position {}", static_cast<int>(cameraPosition));
        return;
    }

    auto bbRect = model->data(model->index(detectionRowNr, DetectionModel::BoundingRect), Qt::DisplayRole).toRectF();
    m_logger->info("New Detection at {:.2f}, {:.2f} ({:.2f}x{:.2f}) - update sub roll aggregations",
        bbRect.x(), bbRect.y(), bbRect.width(), bbRect.height());

    QList<int> affectedSubrolls;
    for (auto idx = 0; idx < m_subRolls.count(); idx++) {
        auto& subRoll = m_subRolls[idx];
        auto leftSubRollPosition = subRoll.startPosition;
        int rightSubRollPosition = 0;
        if (idx < m_subRolls.count() - 1) {
            rightSubRollPosition = m_subRolls.at(idx + 1).startPosition;
        } else {
            // last sub roll
            rightSubRollPosition = m_rollDimensions.right() - m_rightBorderWidth;
        }
        bool leftBorderInsideSubRoll = bbRect.left() >= leftSubRollPosition && bbRect.left() <= rightSubRollPosition;
        bool rightBorderInsideSubRoll = bbRect.right() >= leftSubRollPosition && bbRect.right() <= rightSubRollPosition;
        if (leftBorderInsideSubRoll || rightBorderInsideSubRoll) {
            if (cameraPosition == CameraPosition::TopCameraView) {
                subRoll.topCameraDetections++;
                affectedSubrolls << subRoll.subRollId;
                emit aggregationChanged(CameraPosition::TopCameraView, subRoll.subRollId, subRoll.topCameraDetections);
            } else if (cameraPosition == CameraPosition::BottomCameraView) {
                subRoll.bottomCameraDetections++;
                affectedSubrolls << subRoll.subRollId;
                emit aggregationChanged(CameraPosition::BottomCameraView, subRoll.subRollId, subRoll.bottomCameraDetections);
            }
        }

        if (rightBorderInsideSubRoll) {
            // right border inside sub roll - no need to look further
            break;
        }
    }

    // finally update the model
    model->updateAffectedSubRolls(detectionRowNr, affectedSubrolls);

}

QList<SceneManager::SubRoll> SceneManager::subRolls() const
{
    return m_subRolls;
}

void SceneManager::moveRoll(qreal dy)
{
    m_totalForwardMovement += dy;
    for (SceneParameters& sceneParameters : initializedScenes()) {
        sceneParameters.viewport = sceneParameters.initialViewport;
        sceneParameters.viewport.translate(0.0, m_totalForwardMovement);
    }
    emit rollMoved(dy);
}

qreal SceneManager::totalForwardMovement() const
{
    return m_totalForwardMovement;
}

qreal SceneManager::finalProductWidth() const
{
    return m_rollDimensions.width() - m_leftBorderWidth - m_rightBorderWidth;
}

std::vector<std::reference_wrapper<SceneManager::SceneParameters>> SceneManager::initializedScenes()
{
    std::vector<std::reference_wrapper<SceneParameters>> scenes;
    if (m_topScene.isInitialized) {
        scenes.push_back(m_topScene);
    }
    if (m_bottomScene.isInitialized) {
        scenes.push_back(m_bottomScene);
    }
    return scenes;
}
