#include <QColor>
#include <QString>

using namespace Qt::StringLiterals;

class DetectionColors {
public:

    DetectionColors() = delete;
    ~DetectionColors() = delete;

    static constexpr QColor Inspector_Line = QColor(128,8,8);
    static constexpr QColor Sub_Roll_Border = QColor(255, 255, 0);
    static constexpr QColor Border_Area = QColor(227, 98, 98);

    static constexpr QColor Detection_Item_Pen =QColor(0, 0, 0);
    static constexpr QColor C_Crease = QColor(255, 255, 255);

    static constexpr QColor F_Foreign_Body = QColor(255, 255, 0);

    static constexpr QColor J2_Paper_Splice = QColor(255, 128, 64);
    static constexpr QColor K_Gap_Birdeye = QColor(255, 128, 255);
    static constexpr QColor M_Cured_Material = QColor(0, 0, 255);

    static constexpr QColor P_Fuzzball_Whorl = QColor(255, 0, 0);
    static constexpr QColor R1_Resin_Starved = QColor(128, 255, 255);
    static constexpr QColor R2_Resin_Rich = QColor(0, 128, 0);
    static constexpr QColor S_Mark_or_Scratch = QColor(0, 0, 0);
    static constexpr QColor T_Fibre_Fault = QColor(128, 128, 255);

    static constexpr QColor Mini_Gap = QColor(255, 128, 255);
    static constexpr QColor Not_A_Defect = QColor(0, 0, 0);
    static constexpr QColor Dry_Fibre_Splice = QColor(255, 128, 64);
    static constexpr QColor Mini_Fibre_Fault = QColor(128, 128, 255);
    static constexpr QColor Mini_Unsoluble = QColor(255, 255, 0);
    static constexpr QColor Mini_Resin_Starved = QColor(128, 255, 255);
    static constexpr QColor Mini_Fuzzball = QColor(255, 0, 0);
    static constexpr QColor Unknown = QColor(255, 255, 255);

    // not a color, but somehow related
    static constexpr char32_t Cross_Mark_Codepoint = 0x2716; // ✖ HEAVY MULTIPLICATION X
    inline static  QString Default_Defect_Symbol() { return  QString::fromUcs4(&Cross_Mark_Codepoint, 1); }
};
