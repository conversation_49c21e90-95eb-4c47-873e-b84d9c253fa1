#ifndef SELECT_FILTER_WIDGET_H
#define SELECT_FILTER_WIDGET_H

#include "spdlog/spdlog.h"

#include <QWidget>
#include <QImage>
#include <QSharedPointer>

class QShowEvent;
class QResizeEvent;
class QCloseEvent;
class QLabel;
class FilterListModel;

QT_BEGIN_NAMESPACE
namespace Ui {
class SelectFilterWidget;
}
QT_END_NAMESPACE

class SelectFilterWidget : public QWidget
{
    Q_OBJECT
public:
    explicit SelectFilterWidget(FilterListModel* model, QWidget *parent = nullptr);
    ~SelectFilterWidget() = default;

protected:
    void showEvent(QShowEvent *event) override;
    void resizeEvent(QResizeEvent *event) override;
    void closeEvent(QCloseEvent *event) override;

signals:
    void filterWidgetAboutToGetClosed();

private:
    void initialize(FilterListModel* model);

    Ui::SelectFilterWidget *ui;
    std::shared_ptr<spdlog::logger> m_logger;

};

#endif // SELECT_FILTER_WIDGET_H
