#ifndef ROLLSCENE_H
#define ROLLSCENE_H

#include <QObject>

#include <QSharedPointer>
#include <QGraphicsScene>
#include <QPointer>
#include <QColor>

#include "detectionmodel.h"

#include "spdlog/spdlog.h"

class QGraphicsItem;
class FilterListModel;

class RollScene : public QGraphicsScene
{
    Q_OBJECT

public:
    enum SceneConfigurationItem {
        SubRollItem = 1,
        LeftBorderItem,
        RightBorderItem,
        InspectorLine
    };
    Q_ENUM(SceneConfigurationItem)

    explicit RollScene(QObject *parent = nullptr);
    ~RollScene();

    void setSceneConfigurationItem(SceneConfigurationItem itemType, qreal offset, qreal width = 0.0);
    void moveInspectorLine(qreal dy);
    QPointF centerOfInspectorLine() const;

    void setDetectionModel(QSharedPointer<DetectionModel> detectionModel);
    QSharedPointer<DetectionModel> detectionModel() const;

    void setFilterListModel(FilterListModel *filterListModel);
    FilterListModel* filterListModel() const;

signals:
   void detectionModelChanged();

private:
    void updateFilterData(int filterRow);
    void addDetectionToScene(int atIndex);
    QGraphicsItem* addDefectReport(const QRectF& bBox, const QColor& color, int index, bool cutoutCircle = false);
    QGraphicsItem* addIsraDefect(const QRectF& bBox, const QColor& color, int index);
    QGraphicsItem* addDefectWithoutIcon(const QRectF& bBox, const QColor& color, int index);
    QGraphicsItem* createBoundingRectItem(const QRectF& bBox, const QColor& color, int index, bool showBorder = true) const;

    void setDetectionItemVisible(DetectionModel::FailureType failureType, bool isVisible);

    // Inspector item is managed by QGraphicsScene
    QGraphicsItem *m_inspectorItem { nullptr };
    QSharedPointer<DetectionModel> m_detectionModel {nullptr};
    QPointer<FilterListModel> m_filterListModel;

    std::shared_ptr<spdlog::logger> m_logger;
};
#endif // ROLLSCENE_H
