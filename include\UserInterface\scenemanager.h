#ifndef SCENEMANAGER_H
#define SCENEMANAGER_H

#include "userinterface_export.h"
#include "spdlog/spdlog.h"

#include <QObject>
#include <QRectF>
#include <QList>
#include <QPointer>
#include <QSharedPointer>
#include <QGraphicsScene>

#include <vector>
#include <functional>

#include "filterlistmodel.h"

class DetectionModel;
class RollScene;

/**
 * @class SceneManager
 * @brief UI-sided management of roll scenes
 *
 * This class provides an interface to store and manage
 * roll scenes for both, lower and upper camera positions,
 * that are rendered by Qt's QGraphicsView framework.
 */
class SceneManager : public QObject
{
    Q_OBJECT

public:

    /**
     * @brief Enum for the possible camera positions
     */
    enum CameraPosition {
        UnknownCameraPosition = 0,
        TopCameraView,
        BottomCameraView
    };
    Q_ENUM(CameraPosition)

    /**
     * @brief Struct containing the roll configuration
     */
    struct RollConfiguration {
        QRectF dimensions;       //< Dimensions of the roll given in cm
        qreal finalProductWidth; //< Final product with of the roll given in cm
        int numberOfSubRolls;    ///< Number of subrolls of this specific rolls
        int startId;             ///< The number where this sub roll starts (subrolls are ordered increasingly from right to left)
    };

    /**
     * @brief Struct containing the sub roll configuration
     */
    struct SubRoll {
        int subRollId;               //< The id of this specific sub roll
        qreal startPosition;         //< Start position in cm
        int topCameraDetections;     //> Amount of detected defects for the top camera position
        int bottomCameraDetections;  //< Amount of detected defects for the bottom camera position
        bool operator==(const SubRoll& other) {
            return subRollId == other.subRollId && startPosition == other.startPosition
            && topCameraDetections == other.topCameraDetections
            && bottomCameraDetections == other.bottomCameraDetections;
        }
        bool operator!=(const SubRoll& other) {
             return !(*this == other);
        }
    };

    /**
     * @brief Constructor
     *
     * @param the parent to insert the SceneManager into Qt's object hierarchy
     */
    explicit SceneManager(QObject *parent = nullptr);

    /**
     * @brief Destructor
     */
    ~SceneManager() = default;


    /**
     * @brief Initialize the the scene manager for the given camera position
     *        After calling this method, a QGraphicsView can render the
     *        roll scene for the given camera, and detection can be written
     *        to the detection model
     *
     * @param CameraPosition position the camera position to which the initialized scene is attached
     * @param  const QRectF& viewport the camera viewport in cm coordinates
     */
    void initializeSceneForCamera(CameraPosition position, const QRectF& viewport);

    /**
     * @brief Returns the scene for the given camera
     *        the scene inherits from QGraphicsScene. meaning it can be rendered
     *        by a QGraphicsView
     *
     * @param CameraPosition position the camera position
     * @return the roll scene
     */
    RollScene* sceneForCamera(CameraPosition position);

    /**
     * @brief Returns the detection model for the given camera
     *        whenever a detection occurs, it must be added to the modle
     *        Scene and attached views are upadted automatically
     *
     * @param CameraPosition position the camera position
     * @return the detection model
     */
    QSharedPointer<DetectionModel> detectionModel(CameraPosition position) const;

    /**
     * @brief Checks if the roll scene has been initialized for the
     *        given camera position
     *
     * @param CameraPosition position the camera position
     * @return true, if the scene has been initialized, false otherwise
     */
    bool isInitialized(CameraPosition position) const;

    /**
     * @brief Returns the viewport for the given camera position
     *        The viewport is given in cm and is the viewport
     *        that has been used to initialize the scene translated
     *        by the roll movement
     *
     * @param CameraPosition position the camera position
     * @return a QRectF containing the viewport
     */
    QRectF cameraViewport(CameraPosition position) const;

    /**
     * @brief Initializes the roll configuration (the roll dimensions and sub roll configuration)
     *         After calling the method, the roll scene consists of n sub rolls,
     *         and left and right borders (defined by the roll dimensions subtracted
     *         by the final product width)
     *
     * @param RollConfiguration configuration the roll configuration
     */
    void setRollConfiguration(const RollConfiguration& configuration);

    /**
     * @brief Returns the roll dimensions as defined by calling
     *        setRollConfiguration(const RollConfiguration& configuration)
     *
     * @return a QRectF containing the roll dimensions
     */
    QRectF rollDimensions() const;

    /**
     * @brief Returns the sub rolls that have been computed by calling
     *        setRollConfiguration(const RollConfiguration& configuration)
     *
     * @return QList<SubRoll> a list containing all subrolls
     */
    QList<SubRoll> subRolls() const;

    /**
     * @brief Returns the final product width that has been computed by calling
     *        setRollConfiguration(const RollConfiguration& configuration)
     *
     * @return qreal the final product width
     */
    qreal finalProductWidth() const;

    /**
     * @brief Returns the total movement of the roll
     *        Whenever the roll moves, the slot moveRoll should be called to keep
     *        the scene manager in sync
     *
     *
     * @return qreal the total roll movement
     */
    qreal totalForwardMovement() const;

    /**
     * @brief Returns the left border width that has been computed by calling
     *        setRollConfiguration(const RollConfiguration& configuration)
     *
     * @return qreal the left border width
     */
    qreal leftBorderWidth() const;

    /**
     * @brief Returns the right border width that has been computed by calling
     *        setRollConfiguration(const RollConfiguration& configuration)
     *
     * @return qreal the right border width
     */
    qreal rightBorderWidth() const;

    /**
     * @brief Returns the filter list modelt
     *
     * @return  the FilterListModel defines which defects should be filtered out
     */
    FilterListModel* filterListModel() const;

public slots:
    /**
     * @brief Moves the roll by dy (given in cm)
     *        Attached QGraphicViews should connect to the rollMoved signal
     *        to scroll the visualized scene by dy cm
     *
     * @param qreal dy the delta in vertical movement given in cm
     */
    void moveRoll(qreal dy);

signals:
    /**
     * @brief Signal that is emitted when the roll dimensions have changed
     */
    void rollDimensionsChanged();
    /**
     * @brief Signal that is emitted when the roll configuration has changed
     */
    void rollConfigurationChanged();
    /**
     * @brief Signal that is emitted when the detected amount of defects on
     *        a subroll has changed
     *
     * @param CameraPosition cameraPosition the camera position for which the
     *                       aggregation has changed
     * @param int subRollNr the subroll number (the id) for which the aggregation has changed
     * @param int totalDetections the new amount of total defections
     */
    void aggregationChanged(CameraPosition cameraPosition, int subRollNr, int totalDetections);
    /**
     * @brief Signal that is emitted when the roll has been moved
     *
     * @param qreal dy the movement of the roll in cm
     */
    void rollMoved(qreal dy);

private:
    struct SceneParameters {
        QRectF initialViewport;
        QRectF viewport;
        RollScene *scene { nullptr };
        // ToDo: should be QPointer
        QSharedPointer<DetectionModel> model;
        bool isInitialized = false;
        bool operator==(const SceneParameters& other) {
            return initialViewport == other.initialViewport
                && viewport == other.viewport 
                && scene == other.scene
                && model == other.model
                && isInitialized == other.isInitialized;
        }
        bool operator!=(const SceneParameters& other) {
             return !(*this == other);
        }
    };

    void setNumberOfSubRolls(int numberOfSubRolls, qreal finalProductWidth, int startId);
    void updateSubRollAggregations(CameraPosition cameraPosition, int detectionRowNr);
    std::vector<std::reference_wrapper<SceneParameters>> initializedScenes();

    QRectF m_rollDimensions;
    QList<SubRoll> m_subRolls;
    qreal m_leftBorderWidth {-1.0f};
    qreal m_rightBorderWidth {-1.0f};

    qreal m_totalForwardMovement { 0.0 };

    SceneParameters m_topScene;
    SceneParameters m_bottomScene;

    QPointer<FilterListModel> m_filterListModel;

    std::shared_ptr<spdlog::logger> m_logger;
};
#endif // SCENEMANAGER_H
