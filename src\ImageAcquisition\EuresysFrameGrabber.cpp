#include "EuresysFrameGrabber.h"

#include <iostream>
#include <limits>
#include <algorithm>

#include <cuda_runtime.h>
#include <cuda.h>

#include "ImageAcquisition/check.h"

#include "spdlog/spdlog.h"
#include "spdlog/sinks/stdout_color_sinks.h"

#include <EGrabber.h>


namespace HVIS
{
namespace Internal
{
// Internal class handling low-level frame grabber operations
class EuresysFrameGrabberInternal : public Euresys::EGrabberCallbackSingleThread {
public:
    EuresysFrameGrabberInternal(const Euresys::EGrabberCameraInfo& camInfo)
        : Euresys::EGrabberCallbackSingleThread(camInfo),
        m_running(false),
        m_initialized(false),
        m_userCallback(nullptr)
    {
        m_logger = spdlog::get("EuresysFrameGrabberInternal");
        if (!m_logger) {
            m_logger = spdlog::stdout_color_mt("EuresysFrameGrabberInternal");
            m_logger->set_level(spdlog::level::debug);
            m_logger->debug("Internal Logger created");
        }

        m_logger->debug("Internal Constructor creation");
        m_logger->debug("Internal Constructor done: m_running = {}, m_initialized = {}", m_running, m_initialized);
    }

    ~EuresysFrameGrabberInternal()
    {
        m_logger->debug("Internal Constructor shutdown");
        Euresys::EGrabberCallbackSingleThread::shutdown();
    }

    void allocBuffers(size_t numBuffers)
    {
        // TODO: allocate in GPU

        m_logger->debug("Reallocating buffers: {}", numBuffers);
        Euresys::EGrabberCallbackSingleThread::reallocBuffers(numBuffers);
        m_initialized = true;
        m_logger->debug("Buffers reallocated, m_initialized ={}", m_initialized);
    }

    void allocateAndAnnounceBuffers(size_t numBuffers)
    {
        initCuda();

        m_pinnedMemory.resize(numBuffers, nullptr);

        std::for_each(m_pinnedMemory.begin(), m_pinnedMemory.end(),
            [this](const auto& ptr)
            {
                if (ptr)
                {
                    m_logger->error("pinnedMemory items should all be NULL");
                    throw std::runtime_error("pinnedMemory items should all be NULL");
                }
            }
        );
        
        size_t bufferSize = getWidth() * getHeight() * 3;
        for (size_t i = 0; i < m_pinnedMemory.size(); ++i)
        {
            unsigned char *ptr, *devicePtr;
            checkCudaErrors(cudaSetDeviceFlags(cudaDeviceMapHost));
            checkCudaErrors(cudaHostAlloc(&ptr, bufferSize, cudaHostAllocMapped));
            m_pinnedMemory[i] = ptr;
            checkCudaErrors(cudaHostGetDevicePointer(&devicePtr, ptr, 0));
            Euresys::EGrabberCallbackSingleThread::announceAndQueue(Euresys::UserMemory(ptr, bufferSize, devicePtr));
            // Print pointer address
            m_logger->debug("Announced buffer {} at address {}", i, (void*)ptr);
            m_logger->debug("Announced buffer {} at device address {}", i, (void*)devicePtr);
        }

        m_initialized = true;
    }

    void startAcquisition()
    {
        m_logger->debug("Acquisition start");
        if (m_initialized)
        {
            try
            {
                Euresys::EGrabberCallbackSingleThread::start();
                m_running = true;
                m_logger->debug("Started: m_running ={}", m_running);
            }
            catch (const std::exception& e)
            {
                m_logger->error("Start failed: {}", e.what());
            }
        }
    }

    void stopAcquisition()
    {
        m_logger->debug("Acquisition stop");
        if (m_running)
        {
            Euresys::EGrabberCallbackSingleThread::stop();
            m_running = false;
        }
    }

    void setCallback(Internal::CallbackFunc callback)
    {
        m_logger->debug("Setting callback");
        m_userCallback = callback;
    }

    bool isAcquisitionStarted() const
    {
        m_logger->debug("isAcquisitionStarted: m_running ={}", m_running);
        return m_running;
    }

    bool isInitialized() const
    {
        m_logger->debug("isInitialized: m_initialized ={}", m_initialized);
        return m_initialized;
    }

protected:
    void onNewBufferEvent(const Euresys::NewBufferData& data) override
    {
        m_logger->debug("onNewBufferEvent called");
        if (m_userCallback)
        {
            Euresys::Buffer buffer(data);
            uint8_t* bufferData = static_cast<uint8_t*>(buffer.getUserPointer());
            size_t width = buffer.getInfo<size_t>(*this, Euresys::gc::BUFFER_INFO_WIDTH);
            size_t height = buffer.getInfo<size_t>(*this, Euresys::gc::BUFFER_INFO_DELIVERED_IMAGEHEIGHT);
            size_t size = buffer.getInfo<size_t>(*this, Euresys::gc::BUFFER_INFO_SIZE);
            buffer.saveToDisk(*this, "C:/Users/<USER>/repos/HVIS_CudaProcessing/test_images/test8.1.jpeg");
            
            // TODO: Does it has sense so the user can do whatever he wants with
            // the frame or we are going to use the frame always in the same way??
            // Indeed this is needed for showing the frame, transfering to other processes
            // but do we need this m_userCallback running on a different thread so it doesnt
            // block the main thread acquisition?
            m_userCallback(bufferData, width, height, size, 0);
        }
    }

private:
    bool m_running;
    bool m_initialized;
    CallbackFunc m_userCallback;
    std::shared_ptr<spdlog::logger> m_logger;
    std::vector<unsigned char*> m_pinnedMemory;

    void cleanupCuda()
    {
        m_logger->debug("Cleaning up CUDA");
        checkCudaErrors(cudaDeviceReset());
    }

    void initCuda()
    {
        cleanupCuda();
        m_logger->debug("Initializing CUDA");
        int device = 0;
        cudaDeviceProp prop = {0};
        checkCudaErrors(cudaGetDeviceProperties(&prop, device));
        if (prop.maxThreadsPerBlock < 1024)
        {
            throw std::runtime_error("CUDA device has not enough threads per block");
        }
        if (!prop.canMapHostMemory)
        {
            throw std::runtime_error("CUDA device cannot map host memory");
        }
        if (!prop.deviceOverlap)
        {
            throw std::runtime_error("CUDA device do not support overlaps, multi streams were invalid!");
        }
        checkCudaErrors(cudaSetDevice(device));
    }
};
} // namespace Internal


// EuresysFrameGrabber Implementation

EuresysFrameGrabber::EuresysFrameGrabber()
{
    m_logger = spdlog::get("EuresysFrameGrabber");
    if (!m_logger) {
        m_logger = spdlog::stdout_color_mt("EuresysFrameGrabber");
        m_logger->set_level(spdlog::level::debug);
        m_logger->debug("EuresysFrameGrabber logger created");
    } else
    {
        m_logger->debug("EuresysFrameGrabber logger already created");
    }
    
    m_logger->debug("Calling EuresysFrameGrabber constructor");
}

// EuresysFrameGrabber::EuresysFrameGrabber(size_t cameraIndex)
// {
//     initialize();
//     selectCamera(cameraIndex);
// }

EuresysFrameGrabber::~EuresysFrameGrabber() = default;

void EuresysFrameGrabber::setupLoggers(std::vector<spdlog::sink_ptr> t_sinks)
{
    auto euresys_logger = std::make_shared<spdlog::logger>("EuresysFrameGrabber", t_sinks.begin(), t_sinks.end());
    auto internal_logger = std::make_shared<spdlog::logger>("EuresysFrameGrabberInternal", t_sinks.begin(), t_sinks.end());

    spdlog::register_logger(euresys_logger);
    spdlog::register_logger(internal_logger);

    euresys_logger->set_level(spdlog::level::debug);
    internal_logger->set_level(spdlog::level::debug);
}

void EuresysFrameGrabber::initialize(EuresysProducer t_producer)
{
    try
    {
        switch (t_producer)
        {
            case EuresysProducer::Coaxlink:
                m_genTL = std::make_unique<Euresys::EGenTL>(Euresys::Coaxlink());
                break;
            case EuresysProducer::Grablink:
                m_genTL = std::make_unique<Euresys::EGenTL>(Euresys::Grablink());
                break;
            case EuresysProducer::Gigelink:
                m_genTL = std::make_unique<Euresys::EGenTL>(Euresys::Gigelink());
                break;
            case EuresysProducer::Playlink:
                m_genTL = std::make_unique<Euresys::EGenTL>(Euresys::Playlink());
                break;
            default:
                throw std::runtime_error("Producer is not available");
        }

        m_discovery = std::make_unique<Euresys::EGrabberDiscovery>(*m_genTL);
        m_discovery->discover();

        // Store discovered cameras
        for (size_t i = 0; i < m_discovery->cameraCount(); ++i)
        {
            m_cameras.push_back(m_discovery->cameras(static_cast<int>(i)));
        }

        if (m_cameras.empty())
        {
            throw std::runtime_error("No cameras discovered");
        }
    }
    catch (const std::exception& e)
    {
        m_logger->error("Initialization failed: {}", e.what());
        throw;
    }
}

void EuresysFrameGrabber::selectCamera(size_t cameraIndex)
{
    if (m_cameras.empty())
    {
        throw std::runtime_error("No cameras available to select");
    }

    if (cameraIndex == SIZE_MAX)
    {
        // Auto-select if only one camera or prompt user
        if (m_cameras.size() == 1)
        {
            cameraIndex = 0;
        }
        else
        {
            std::cout << "Multiple cameras detected. Please select a camera:\n";
            for (size_t i = 0; i < m_cameras.size(); ++i)
            {
                // FIXME: std::string cameraId = m_discovery->cameras(i).getInfo<std::string>(Euresys::gc::DEVICE_INFO_MODEL);
                // FIXME: std::cout << "[" << i << "] " << cameraId << "\n";
            }
            std::cout << "Enter camera index: ";
            std::cin >> cameraIndex;
            // FIXME: std::cin.ignore(std::numeric_limits<std::streamsize>::max(), '\n'); // Clear input buffer
        }
    }

    if (cameraIndex >= m_cameras.size())
    {
        throw std::runtime_error("Invalid camera index selected");
    }

    m_frameGrabber = std::make_unique<Internal::EuresysFrameGrabberInternal>(m_cameras[cameraIndex]);
}

void EuresysFrameGrabber::allocBuffers(size_t numBuffers)
{
    if (m_frameGrabber)
    {
        m_frameGrabber->allocBuffers(numBuffers);
    }
}

void EuresysFrameGrabber::allocateAndAnnounceGPUBuffers(size_t numBuffers)
{
    if (m_frameGrabber)
    {
        m_frameGrabber->allocateAndAnnounceBuffers(numBuffers);
    }
}

void EuresysFrameGrabber::startAcquisition()
{
    if (m_frameGrabber)
    {
        m_frameGrabber->startAcquisition();
    }
}

void EuresysFrameGrabber::stopAcquisition()
{
    if (m_frameGrabber)
    {
        m_frameGrabber->stopAcquisition();
    }
}

void EuresysFrameGrabber::setCallback(Internal::CallbackFunc callback)
{
    if (m_frameGrabber)
    {
        m_frameGrabber->setCallback(callback);
    }
}

bool EuresysFrameGrabber::isAcquisitionStarted() const
{
    return m_frameGrabber ? m_frameGrabber->isAcquisitionStarted() : false;
}

bool EuresysFrameGrabber::isInitialized() const
{
    return m_frameGrabber ? m_frameGrabber->isInitialized() : false;
}

static std::string eGrabberInfo(const Euresys::EGrabberInfo &info)
{
    std::stringstream ss;
    ss << info.interfaceID + "/" + info.deviceID + "/" + info.streamID;
    if (info.isRemoteAvailable) {
        ss << "  (" + info.deviceModelName + ")";
    }
    return ss.str();
}

std::vector<std::string> EuresysFrameGrabber::getCameraList() const
{
    std::vector<std::string> cameraList;
    std::string cam;

    for (const auto& camera : m_cameras)
    {
        cam += camera.grabbers[0].deviceVendorName + "  ";
        cam += camera.grabbers[0].deviceModelName + " ";
        cam += "(" + camera.grabbers[0].streamDescription + ")";

        cameraList.push_back(cam);
        cam.clear();
    }

    return cameraList;
}

std::vector<std::string> EuresysFrameGrabber::getCameraDescriptionList() const
{
    std::vector<std::string> cameraList;
    std::string cam;

    cameraList.push_back("There are " + std::to_string(m_cameras.size()) + " cameras in the system\n");
    for (const auto& camera : m_cameras)
    {
        cam += " - camera[" + std::to_string(cameraList.size() - 1) + "]\n";
        if (camera.grabbers.size() > 1)
        {
            cam += "    - This is a multi-bank camera composed by the following grabbers\n";
        }
        for (size_t i = 0; i < camera.grabbers.size(); ++i)
        {
            cam += "    - grabbers[" + std::to_string(i) + "]: " + eGrabberInfo(camera.grabbers[i]) + "\n";
            cam += "        - interfaceIndex: " + std::to_string(camera.grabbers[i].interfaceIndex) + "\n";
            cam += "        - deviceIndex: " + std::to_string(camera.grabbers[i].deviceIndex) + "\n";
            // cam += "        - streamIndex: " + std::to_string(camera.grabbers[i].interfaceIndex) + "\n";
            // cam += "        - interfaceID: " + camera.grabbers[i].interfaceID + "\n";
            // cam += "        - deviceID: " + camera.grabbers[i].deviceID + "\n";
            // cam += "        - streamID: " + camera.grabbers[i].streamID + "\n";
            cam += "        - deviceVendorName: " + camera.grabbers[i].deviceVendorName + "\n";
            cam += "        - deviceModelName: " + camera.grabbers[i].deviceModelName + "\n";
            cam += "        - deviceDescription: " + camera.grabbers[i].deviceDescription + "\n";
            cam += "        - streamDescription: " + camera.grabbers[i].streamDescription + "\n";
            // cam += "        - deviceUserID: " + camera.grabbers[i].deviceUserID + "\n";
            cam += "        - deviceSerialNumber: " + camera.grabbers[i].deviceSerialNumber + "\n";
            // cam += "        - deviceLicenseStatus: " + camera.grabbers[i].deviceLicenseStatus + "\n";
            // cam += "        - tlType: " + camera.grabbers[i].tlType + "\n";
            // cam += "        - firmwareStatus: " + camera.grabbers[i].firmwareStatus + "\n";;
            // cam += "        - fanStatus: " + camera.grabbers[i].fanStatus + "\n";
            cam += "        - licenseStatus: " + camera.grabbers[i].licenseStatus + "\n";
        }

        cameraList.push_back(cam);
        cam.clear();
    }

    return cameraList;
}

static void showElements(const std::string &moduleName, const std::vector<std::string> &vect) {
    std::cout << moduleName << " features: " << std::endl;
    typedef std::vector<std::string>::const_iterator string_iterator;
    for (string_iterator it = vect.begin(); it != vect.end(); it++) {
        std::cout << "  " << *it << std::endl;
    }
}

void EuresysFrameGrabber::configureEuresysDevices()
{
    showElements("RemoteModue", m_frameGrabber->getStringList<Euresys::RemoteModule>(Euresys::query::features()));
}
} //namespace HVIS
