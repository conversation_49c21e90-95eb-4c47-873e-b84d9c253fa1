#ifndef DETECTION_MODEL
#define DETECTION_MODEL

#include "userinterface_export.h"

#include <QObject>
#include <QAbstractTableModel>
#include <QRectF>
#include <QImage>
#include <QVariant>
#include <QModelIndex>

/**
 * @class DetectionModel
 * @brief Model to store detections
 *
 * Acts as an interface between the detection pipeline and user interface
 * For an overview check also the documentation for QAbstractItemModel
 */
class DetectionModel : public QAbstractTableModel
{
    Q_OBJECT

public:

    using AffectedSubRollList = QList<int>;

    /**
     * @brief Enum for the model columns
     */
    enum DetectionColumnRoles {
        Id = 0,                   //< the id of the detection (an uuid string assigned by the model)
        FailureClass,             //< The failure class (see below for all possible failure classes
        FailureString,            //< The failure class as human readable string
        BoundingRect,             //< The bounding rect of the failure
        ConfidenceScore,          //< The confidence of the detection (the probability that the detection is correct)
        DetectionImage,           //< The image to visualize the failure
        AffectedSubRolls,         //< The affected sub rolls
    };
    Q_ENUM(DetectionColumnRoles)

    /**
     * @brief Enum for the differnet possible failure types
     */
    enum FailureType {
        Unknown = 0,
        C_Crease,
        E_Edge_Defect,
        F_Foreign_Body,
        J1_Prepreg_Splice,
        J2_Paper_Splice,
        K_Gap_Birdeye,
        M_Cured_Material,
        O_Other_Defect,
        P_Fuzzball_Whorl,
        R1_Resin_Starved,
        R2_Resin_Rich,
        S_Mark_or_Scratch,
        T_Fibre_Fault,
        Mini_Gap,
        Not_A_Defect,
        Dry_Fibre_Splice,
        Mini_Fibre_Fault,
        Mini_Unsoluble,
        Mini_Resin_Starved,
        Mini_Fuzzball,
        // only for development; do not use in production
        Development_Test_1,
        Development_Test_2
    };
    Q_ENUM(FailureType)

    /**
     * @brief Struct that stores a detection
     *        Check the description for the enum DetectionColumnRoles
     */
    struct Detection {
        FailureType failure {FailureType::Unknown};
        QRectF boundingRect;
        qreal confidenceScore;
        QImage detectionImage;
        AffectedSubRollList affectedSubRolls;
        bool operator==(const Detection& other) {
            return failure == other.failure && boundingRect == other.boundingRect
                   && confidenceScore == other.confidenceScore
                   && detectionImage == other.detectionImage
                   && affectedSubRolls == other.affectedSubRolls;
        }
        bool operator!=(const Detection& other) {
            return !(*this == other);
        }
    };

    /**
     * @brief Constructor
     */
    explicit DetectionModel(QObject *parent = nullptr);

    /**
     * @brief Destructor
     */
    ~DetectionModel() = default;

    /**
     * @brief Converts detection failure classes to QString
     *        Note: the returned strings are already translated
     *
     * @param failureType the failure txpe to convert
     * @returns the failure type converted to a translated QString
     */
    static QString failureTypeToString(FailureType  failureType);

    /**
     * @brief the number of detected failures
     *
     * Overrides QAbstractItemModel::rowCount()
     */
    int rowCount(const QModelIndex &parent = QModelIndex()) const override;

    /**
     * @brief should return the number of possible DetectionColumnRoles enum values
     *
     * Overrides QAbstractItemModel::columnCount()
     */
    int columnCount(const QModelIndex &parent = QModelIndex()) const override;

    /**
     * @brief returns model data
     *
     * Overrides QAbstractItemModel::data()
     */
    QVariant data(const QModelIndex &index, int role = Qt::DisplayRole) const override;

    /**
     * @brief resets the model (sets the number of detections to 0)
     */
    void reset();

    /**
     * @brief Adds an detection to the model
     */
    void addDetection(const Detection& detection);

    /**
     * @brief Updates the detection in the model
     *
     * @param index The index to update; must be in the range 0, rowCount() - 1
     * @returns true, if the model has been updated successfully
     */
    bool updateDetection(int index, const Detection& detection);

    /**
     * @brief Updates the affected subrolls
     *
     * @param index The index to update; must be in the range 0, rowCount() - 1
     * @returns const QList<int> &affectedSubRolls the subrolls that are affected by this detection
     */
    bool updateAffectedSubRolls(int index, const QList<int> &affectedSubRolls);

private:
    // keep these lists always in sync
    QList<Detection> m_detections;
    QList<QString> m_detectionIds;

};
#endif // DETECTION_MODEL
