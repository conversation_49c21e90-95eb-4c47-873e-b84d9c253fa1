#include "rollscene.h"

#include <QGraphicsView>
#include <QAbstractItemModel>
#include <QGraphicsSimpleTextItem>
#include <QGraphicsItem>
#include <QGraphicsItemGroup>
#include <QGraphicsLineItem>
#include <QGraphicsRectItem>
#include <QGraphicsPathItem>
#include <QModelIndex>
#include <QFontMetrics>
#include <QPainterPath>
#include <QColor>
#include <QPen>
#include <QBrush>
#include <QFont>
#include <QList>
#include <QVariant>

#include "detectioncolors.h"
#include "filterlistmodel.h"

#include "spdlog/sinks/stdout_color_sinks.h"

using namespace Qt::StringLiterals;

static const uint Detection_Item_Metadata_Id = 0;

// avoid a glitch occuring with some screen resolutions and dpi scaling configurations on Windows
class AdjustedGroupItem : public QGraphicsItemGroup
{
public:
    explicit AdjustedGroupItem(QGraphicsItem *parent = nullptr)
        : QGraphicsItemGroup(parent) {}
    ~AdjustedGroupItem() = default;
    QRectF boundingRect() const override {
        QRectF bbRect = QGraphicsItemGroup::boundingRect();
        const qreal margin = 10;
        return bbRect.adjusted(-margin, -margin, margin, margin);
    }
};

RollScene::RollScene(QObject *parent)
    : QGraphicsScene(parent)
{
    m_logger = spdlog::get("UserInterfaceApplication");
    if (!m_logger) {
        m_logger = spdlog::stdout_color_mt("UserInterfaceApplication");
        m_logger->set_level(spdlog::level::debug);
    }
    setBackgroundBrush(QColor(u"#808080"_s));
}

RollScene::~RollScene()
{
    // deleted by parent class
    m_inspectorItem = nullptr;
}

void RollScene::setSceneConfigurationItem(SceneConfigurationItem itemType, qreal offset, qreal width)
{
    if (itemType == RollScene::SubRollItem) {
        QPen dashedLine(DetectionColors::Sub_Roll_Border);
        dashedLine.setStyle(Qt::DashLine);
        dashedLine.setWidth(2);
        QList<qreal> dashes;
        dashes << 2 << 4;
        dashedLine.setDashPattern(dashes);
        dashedLine.setCosmetic(true);
        auto line = addLine(0.0, 0.0, 0.0, sceneRect().height(), dashedLine);
        line->setPos(offset, 0.0);
    } else if (itemType == RollScene::LeftBorderItem || itemType == RollScene::RightBorderItem) {
        QBrush crossedPattern(DetectionColors::Border_Area, Qt::DiagCrossPattern);
        auto borderRect = addRect(0.0, 0.0, width, sceneRect().height(), Qt::NoPen, crossedPattern);
        borderRect->setPos(offset, 0.0);
        // draw border line
        QPen linePen(DetectionColors::Sub_Roll_Border);
        linePen.setWidthF(3.0);
        linePen.setCosmetic(true);
        qreal xCoord = itemType == RollScene::LeftBorderItem ? offset + width : offset;
        auto line = addLine(0.0, 0.0, 0.0, sceneRect().height(),  linePen);
        line->setPos(xCoord, 0.0);
    } else if (itemType == RollScene::InspectorLine) {
        if (!m_inspectorItem) {
            auto currentFont = font();
            currentFont.setPointSize(16);

            QPen p(DetectionColors::Inspector_Line);
            p.setCosmetic(true);
            p.setWidthF(3.0);
            auto lineItem = new QGraphicsLineItem(0.0, 0.0, sceneRect().width(), 0.0);
            lineItem->setPen(p);

            auto textItem = new QGraphicsSimpleTextItem(QObject::tr("Inspector"));
            textItem->setFlag(QGraphicsItem::ItemIgnoresTransformations);
            textItem->setFont(currentFont);
            textItem->setPos(1.0, 10.0);
            textItem->setBrush(DetectionColors::Inspector_Line);

            auto inspectorItem = new AdjustedGroupItem();
            inspectorItem->addToGroup(lineItem);
            inspectorItem->addToGroup(textItem);
            inspectorItem->setPos(0.0, offset);
            addItem(inspectorItem);
            m_inspectorItem = inspectorItem;

        } else {
            m_logger->warn("Inspector item already included to scene - scene cannot manage more than one item.");
        }
    }
}

void RollScene::moveInspectorLine(qreal dy)
 {
    if (m_inspectorItem) {
        m_inspectorItem->setPos(0.0, m_inspectorItem->pos().y() + dy);
    } else {
        m_logger->warn("Cannot update inspector line: No inspector line found");
    }
 }

 QPointF RollScene::centerOfInspectorLine() const
 {
    if (m_inspectorItem)  {
        auto inspectorLineStartPos = m_inspectorItem->pos();
        // center on x axis
        inspectorLineStartPos.setX(sceneRect().width() * 0.5);
        return inspectorLineStartPos;        
    }
    return QPointF();
 }


void RollScene::setDetectionModel(QSharedPointer<DetectionModel> detectionModel)
{
    if (!m_detectionModel.isNull()) {
        m_detectionModel->disconnect(this);
    }

    m_detectionModel = detectionModel;

    if (!m_detectionModel.isNull()) {
        connect(m_detectionModel.data(), &QAbstractItemModel::rowsInserted,
            this, [this](const QModelIndex &, int first, int last) {
                for (auto idx = first; idx <= last; idx++) {
                    this->addDetectionToScene(idx);
                }
            });
    }
}

void RollScene::setFilterListModel(FilterListModel *filterListModel)
{
    if (!m_filterListModel.isNull()) {
        m_filterListModel->disconnect(this);
    }
    m_filterListModel = filterListModel;
    if (!m_filterListModel.isNull()) {
        connect(m_filterListModel.data(), &QAbstractItemModel::dataChanged,
                this, [this](const QModelIndex &topLeft, const QModelIndex &bottomRight, const QList<int> &roles) {
                    if (roles.contains(Qt::CheckStateRole)) {
                        for (auto idx = topLeft.row(); idx <= bottomRight.row(); idx++) {
                            this->updateFilterData(idx);
                        }
                    }
                });
    }
}

void RollScene::updateFilterData(int filterRow)
{
    if (m_filterListModel.isNull()) {
        return;
    }

    auto failureClass = m_filterListModel->data(m_filterListModel->index(filterRow, 0),FilterListModel::RawFailureClassValue).toInt();
    auto checked = m_filterListModel->data(m_filterListModel->index(filterRow, 0), Qt::CheckStateRole).toBool();
    setDetectionItemVisible(DetectionModel::FailureType(failureClass), checked);
}

FilterListModel* RollScene::filterListModel() const
{
    return m_filterListModel;
}

QSharedPointer<DetectionModel> RollScene::detectionModel() const
{
    return m_detectionModel;
}

void RollScene::setDetectionItemVisible(DetectionModel::FailureType failureType, bool isVisible)
{
    if (m_detectionModel.isNull()) {
        return;
    }
    const auto itemList = items();
    for (const auto graphicsItem : itemList) {
        bool ok;
        const auto detectionModelIndex = graphicsItem->data(Detection_Item_Metadata_Id).toInt(&ok);
        if (!ok || detectionModelIndex < 0) {
            continue;
        }


       auto itemFailureClass = m_detectionModel->data(m_detectionModel->index(detectionModelIndex, DetectionModel::FailureClass), Qt::DisplayRole).toInt();
       if (DetectionModel::FailureType(itemFailureClass) == failureType) {
            graphicsItem->setVisible(isVisible);
       }
    }
}


void RollScene::addDetectionToScene(int atIndex)
{
    if (m_detectionModel.isNull()) {
        return;
    }

    QGraphicsItem* addedItem = nullptr;

    auto failureClass = m_detectionModel->data(m_detectionModel->index(atIndex, DetectionModel::FailureClass), Qt::DisplayRole).toInt();
    auto bbRect = m_detectionModel->data(m_detectionModel->index(atIndex, DetectionModel::BoundingRect), Qt::DisplayRole).toRectF();
    //auto score = m_detectionModel->data(m_detectionModel->index(atIndex, DetectionModel::ConfidenceScore), Qt::DisplayRole).toReal();

    auto textFont = font();
    textFont.setPointSize(16);
    QFontMetrics fm(textFont);
    auto charWidth = fm.boundingRect(DetectionColors::Default_Defect_Symbol()).width();
    if (failureClass == DetectionModel::Development_Test_1) {
        QPen p(Qt::black);
        p.setWidthF(1.5);
        p.setCosmetic(true);
        QBrush b(Qt::red);
        auto sizeInPixels = 20; 
        auto failure1ClassItem = addRect(QRectF(0.0, 0.0, sizeInPixels, sizeInPixels), p, b);
        failure1ClassItem->setFlag(QGraphicsItem::ItemIgnoresTransformations);
        failure1ClassItem->setPos(bbRect.x(), bbRect.y());
        failure1ClassItem->setData(Detection_Item_Metadata_Id, QVariant(atIndex));

        auto innerText = new QGraphicsSimpleTextItem(DetectionColors::Default_Defect_Symbol(), failure1ClassItem);
        innerText->setFont(textFont);
        innerText->setPos(0.5 * (sizeInPixels - charWidth), -2.0);
        innerText->setBrush(Qt::black);
        addedItem = failure1ClassItem;
    } else if (failureClass == DetectionModel::Development_Test_2) {
        //addIsraDefect(bbRect, Qt::yellow, atIndex);
        addedItem = addDefectReport(bbRect, Qt::yellow, atIndex, true);
    } else if (failureClass == DetectionModel::C_Crease) {
        addedItem = addDefectReport(bbRect, DetectionColors::C_Crease, atIndex, true);
    } else if (failureClass == DetectionModel::E_Edge_Defect) {
        addedItem = addDefectWithoutIcon(bbRect, Qt::black, atIndex);
    } else if (failureClass == DetectionModel::F_Foreign_Body) {
        addedItem = addDefectReport(bbRect, DetectionColors::F_Foreign_Body, atIndex);
    } else if (failureClass == DetectionModel::J1_Prepreg_Splice) {
        addedItem = addDefectWithoutIcon(bbRect, Qt::black, atIndex);
    } else if (failureClass == DetectionModel::J2_Paper_Splice) {
        addedItem = addDefectReport(bbRect, DetectionColors::J2_Paper_Splice, atIndex, true);
    } else if (failureClass == DetectionModel::K_Gap_Birdeye) {
        addedItem = addDefectReport(bbRect, DetectionColors::K_Gap_Birdeye, atIndex);
    } else if (failureClass == DetectionModel::M_Cured_Material) {
        addedItem = addDefectReport(bbRect, DetectionColors::M_Cured_Material, atIndex, true);
    } else if (failureClass == DetectionModel::O_Other_Defect) {
        addedItem = addDefectWithoutIcon(bbRect, Qt::black, atIndex);
    } else if (failureClass == DetectionModel::P_Fuzzball_Whorl) {
        addedItem = addDefectReport(bbRect, DetectionColors::P_Fuzzball_Whorl, atIndex);
    } else if (failureClass == DetectionModel::R1_Resin_Starved) {
        addedItem = addDefectReport(bbRect, DetectionColors::R1_Resin_Starved, atIndex, true);
    } else if (failureClass == DetectionModel::R2_Resin_Rich) {
        addedItem = addDefectReport(bbRect, DetectionColors::R2_Resin_Rich, atIndex, true);
    } else if (failureClass == DetectionModel::S_Mark_or_Scratch) {
        addedItem = addDefectReport(bbRect, DetectionColors::S_Mark_or_Scratch, atIndex, true);
    } else if (failureClass == DetectionModel::T_Fibre_Fault) {
        addedItem = addDefectReport(bbRect, DetectionColors::T_Fibre_Fault, atIndex);
    } else if (failureClass == DetectionModel::Mini_Gap) {
        addedItem = addIsraDefect(bbRect, DetectionColors::Mini_Gap, atIndex);
    } else if (failureClass == DetectionModel::Not_A_Defect) {
        addedItem = addIsraDefect(bbRect, DetectionColors::Not_A_Defect, atIndex);
    } else if (failureClass == DetectionModel::Dry_Fibre_Splice) {
        addedItem = addIsraDefect(bbRect, DetectionColors::Dry_Fibre_Splice, atIndex);
    } else if (failureClass == DetectionModel::Mini_Fibre_Fault) {
        addedItem = addIsraDefect(bbRect, DetectionColors::Mini_Fibre_Fault, atIndex);
    } else if (failureClass == DetectionModel::Mini_Unsoluble) {
        addedItem = addIsraDefect(bbRect, DetectionColors::Mini_Unsoluble, atIndex);
    } else if (failureClass == DetectionModel::Mini_Resin_Starved) {
        addedItem = addIsraDefect(bbRect, DetectionColors::Mini_Resin_Starved, atIndex);
    } else if (failureClass == DetectionModel::Mini_Fuzzball) {
        addedItem = addIsraDefect(bbRect, DetectionColors::Mini_Fuzzball, atIndex);
    }

    if (addedItem != nullptr && m_filterListModel != nullptr) {
        auto isFiltered = m_filterListModel->isFailureClassFiltered(DetectionModel::FailureType(failureClass));
        addedItem->setVisible(!isFiltered);
    }
}

QGraphicsItem* RollScene::addDefectReport(const QRectF& bBox, const QColor& color, int index, bool cutoutCircle)
{
    auto boundingRectItem = createBoundingRectItem(bBox, color, index);
    addItem(boundingRectItem);

    QPen p(Qt::black);
    p.setWidthF(1.5);
    p.setCosmetic(true);

    static const qreal OUTER_RADIUS = 12;
    static const qreal INNER_RADIUS = 5.5;
    QPainterPath path;
    // outer circle
    path.addEllipse(QRectF(0.0, 0.0, OUTER_RADIUS * 2.0, OUTER_RADIUS * 2.0));
    // inner circle
    if (cutoutCircle) {
        QRectF innerRect(0.0, 0.0, INNER_RADIUS * 2.0, INNER_RADIUS * 2.0);
        innerRect.translate(OUTER_RADIUS - INNER_RADIUS, OUTER_RADIUS - INNER_RADIUS);
        path.addEllipse(innerRect);
    }

    auto pathItem = new QGraphicsPathItem(path, boundingRectItem);
    pathItem->setFlag(QGraphicsItem::ItemIgnoresTransformations);
    const auto aspectRatioCorrection = 1.5;
    QPointF center = boundingRectItem->mapFromScene(bBox.center()) - QPointF(2.0 * OUTER_RADIUS, -2.0 * aspectRatioCorrection * OUTER_RADIUS);
    pathItem->setPos(center);
    pathItem->setPen(p);
    pathItem->setBrush(color);
    return boundingRectItem;
}

QGraphicsItem* RollScene::addIsraDefect(const QRectF& bBox, const QColor& color, int index)
{
    auto boundingRectItem = createBoundingRectItem(bBox, color, index);
    addItem(boundingRectItem);

    auto textFont = font();
    textFont.setPointSize(20);
    QFontMetrics fm(textFont);
    auto fontHeight = fm.height();
    auto charWidth = fm.boundingRect(DetectionColors::Default_Defect_Symbol()).width();
    QPen p(Qt::black);
    p.setWidthF(1.5);
    p.setCosmetic(true);

    auto textItem = new QGraphicsSimpleTextItem(DetectionColors::Default_Defect_Symbol(), boundingRectItem);
    textItem->setFont(textFont);
    textItem->setFlag(QGraphicsItem::ItemIgnoresTransformations);
    // ToDo: Consider correct aspect ratio
    const auto aspectRatioCorrection = 1.5;
    textItem->setPos(0.5 * bBox.width() - charWidth, aspectRatioCorrection * 0.5 * (bBox.height() + fontHeight));
    textItem->setBrush(color);
    textItem->setPen(p);
    return boundingRectItem;
}

QGraphicsItem* RollScene::addDefectWithoutIcon(const QRectF& bBox, const QColor& color, int index)
{
    auto boundingRectItem = createBoundingRectItem(bBox, color, index);
    addItem(boundingRectItem);
    return boundingRectItem;
}

QGraphicsItem* RollScene::createBoundingRectItem(const QRectF& bBox, const QColor& color, int index, bool showBorder) const
{
    QPen p;
    if (showBorder) {
        p = QPen(color);
        p.setWidthF(1.5);
        p.setCosmetic(true);
        p.setStyle(Qt::DashLine);
    } else {
        p = QPen(Qt::NoPen);
    }
    auto boundingRectItem = new QGraphicsRectItem(QRectF(0.0, 0.0, bBox.width(), bBox.height()));
    boundingRectItem->setData(Detection_Item_Metadata_Id, QVariant(index));
    boundingRectItem->setPen(p);

    boundingRectItem->setPos(bBox.x(), bBox.y());
    return boundingRectItem;
}

