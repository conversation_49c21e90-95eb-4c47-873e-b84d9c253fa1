# Add all source modules
add_subdirectory(ImageAcquisition)
# add_subdirectory(ImageOperations)
# add_subdirectory(InferenceEngine)
add_subdirectory(UserInterface)

# ------------- <PERSON><PERSON><PERSON> (Manager) -------------
add_library(Manager STATIC)

# We don't want to add all the cpp files since we could have different types of managers in the future
target_sources(Manager 
    PRIVATE
        PrepregManager.cpp
)

target_include_directories(Manager 
    PUBLIC
        $<BUILD_INTERFACE:${CMAKE_SOURCE_DIR}/include>
)

# Let downstream targets know what to link if they use "Manager"
target_link_libraries(Manager
    PUBLIC
        ImageAcquisition
        UserInterface
        spdlog::spdlog
)