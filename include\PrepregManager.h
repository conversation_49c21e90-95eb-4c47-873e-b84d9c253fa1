#pragma once
#include "ImageAcquisition/EuresysFrameGrabber.h"

#include <memory>
#include <string>

#include "spdlog/spdlog.h"


class PrepregManager
{
public:
    PrepregManager();
    ~PrepregManager();

    static void setupLoggers(std::vector<spdlog::sink_ptr> t_sinks);

    int show();
    void runImageAcquisition();

private:
    std::unique_ptr<HVIS::EuresysFrameGrabber> m_frameGrabber;
    // std::unique_ptr<MainWindow> m_uiManager;
    // QApplication* m_app;

    inline static std::shared_ptr<spdlog::logger> m_logger;
};