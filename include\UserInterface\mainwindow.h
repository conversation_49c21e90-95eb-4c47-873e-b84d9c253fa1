#ifndef MAINWINDOW_H
#define MAINWINDOW_H

#include "userinterface_export.h"
#include "videostreamwindow.h"
#include "selectfilterwidget.h"

#include <QMainWindow>
#include <QImage>
#include <QPointer>

class SceneManager;

QT_BEGIN_NAMESPACE
namespace Ui {
class MainWindow;
}
QT_END_NAMESPACE

/**
 * @class MainWindow
 * @brief The main entry point for everything related to the user interface
 *
 * Wen adding a user interface to the detection pipeline, create a MainWindow,
 * set a scene manager and update the detection model for the camera position
 */
class MainWindow : public QMainWindow
{
    Q_OBJECT

public:

    /**
     * @brief Constructor
     */
    explicit MainWindow(QWidget *parent = nullptr);

    /**
     * @brief Destructor
     */
    ~MainWindow();

    /**
     * @brief Sets the scene manager
     *
     * The scene manager acts as an interface between the detection pipeline
     * and the user interface. It maanges the roll scenes for the different
     * camera position (top and down camera). You can request the detection
     * model for a specific camera position and add detections to the model.
     */
    void setSceneManager(SceneManager *sceneManager);

signals:
    void actionTriggered();

    /**
     * @brief Signal that is emitted when the user requests a debug window
     *        showing the video stream.
     */
    void videoStreamStartRequested();

    /**
     * @brief Signal that is emitted when the user closes the debug window
     *        showing the video stream.
     */
    void videoStreamStopRequested();

public slots:
    /**
     * @brief Setd the current frame
     *
     * This slot should be called after the videoStreamStartRequested() signal has
     * been emitted. It should not be called before the signal has been emitted or
     * after the videoStreamStopRequested(9 has been emitted.
     *
     * @param const QImage& frame a QImage showing the current frame
     * @param size_t frameId optional; the current frame id
     */
    void updateVideoFrame(const QImage& frame, size_t frameId = 0);

private slots:
    void showVideoStreamWindow();
    // currently, this method opens its own window, but the widget may be displayed
    // inside the main window in the future
    void openSelectFilterList();

private:
    void initialize();

    Ui::MainWindow *ui;
    QPointer<VideoStreamWindow> m_videoStreamWindow;
    QPointer<SelectFilterWidget> m_filterListWidget;

    std::shared_ptr<spdlog::logger> m_logger;

};
#endif // MAINWINDOW_H
