#include "selectfilterwidget.h"
#include "ui_selectfilterwidget.h"

#include "filterlistmodel.h"

#include "spdlog/sinks/stdout_color_sinks.h"

#include <QPushButton>
#include <QListView>
#include <QString>
#include <QLabel>
#include <QShowEvent>
#include <QResizeEvent>
#include <QCloseEvent>

using namespace Qt::StringLiterals;

SelectFilterWidget::SelectFilterWidget(FilterListModel* model, QWidget* parent)
    : QWidget(parent)
    , ui(new Ui::SelectFilterWidget)
{
    ui->setupUi(this);
    m_logger = spdlog::get("UserInterfaceApplication");
    if (!m_logger) {
        m_logger = spdlog::stdout_color_mt("UserInterfaceApplication");
        m_logger->set_level(spdlog::level::debug);
    }
    m_logger->info("VideoStreamWindow opened ...");
    initialize(model);
}

void SelectFilterWidget::initialize(FilterListModel* model)
{
    connect(ui->closeButton, &QPushButton::clicked, this, &QWidget::close);
    ui->filterListView->setModel(model);
}

void SelectFilterWidget::showEvent(QShowEvent *event)
{
    Q_UNUSED(event);
    setWindowTitle(tr("Select Filter"));
    resize(600, 600);
}

void SelectFilterWidget::resizeEvent(QResizeEvent *event)
{
    Q_UNUSED(event);
}

void SelectFilterWidget::closeEvent(QCloseEvent *event)
{
    Q_UNUSED(event);
    emit filterWidgetAboutToGetClosed();
}

