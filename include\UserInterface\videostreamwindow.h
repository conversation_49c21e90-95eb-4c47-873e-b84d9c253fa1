#ifndef VIDEO_STREAM_WINDOW_H
#define VIDEO_STREAM_WINDOW_H

#include "spdlog/spdlog.h"

#include <QWidget>
#include <QImage>

class QShowEvent;
class QResizeEvent;
class QCloseEvent;
class QLabel;

class VideoStreamWindow : public QWidget
{
    Q_OBJECT
public:
    explicit VideoStreamWindow(QWidget *parent = nullptr);
    ~VideoStreamWindow() = default;

    void updateFrameImage(const QImage& frameImage, size_t frameId = 0);

protected:
    void showEvent(QShowEvent *event) override;
    void resizeEvent(QResizeEvent *event) override;
    void closeEvent(QCloseEvent *event) override;

signals:
    void videoStreamWindowAboutToGetClosed();

private:
    void initialize();

    QLabel *m_frameLabel { nullptr };
    QLabel *m_imageLabel { nullptr };

    std::shared_ptr<spdlog::logger> m_logger;

};

#endif // VIDEO_STREAM_WINDOW_H
