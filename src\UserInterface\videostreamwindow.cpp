#include "videostreamwindow.h"

#include "spdlog/sinks/stdout_color_sinks.h"

#include <QVBoxLayout>
#include <QString>
#include <QSizePolicy>
#include <QPixmap>
#include <QLabel>
#include <QShowEvent>
#include <QResizeEvent>
#include <QCloseEvent>

using namespace Qt::StringLiterals;

VideoStreamWindow::VideoStreamWindow(QWidget* parent)
    : QWidget(parent)
{
    m_logger = spdlog::get("UserInterfaceApplication");
    if (!m_logger) {
        m_logger = spdlog::stdout_color_mt("UserInterfaceApplication");
        m_logger->set_level(spdlog::level::debug);
    }
    m_logger->info("VideoStreamWindow opened ...");
    initialize();
}

void VideoStreamWindow::initialize()
{
    auto verticalLayout = new QVBoxLayout(this);

    m_frameLabel = new QLabel(tr("Not started ..."));
    m_imageLabel = new QLabel();
    m_imageLabel->setSizePolicy(QSizePolicy::MinimumExpanding, QSizePolicy::MinimumExpanding);

    verticalLayout->addWidget(m_frameLabel);
    verticalLayout->addWidget(m_imageLabel);
}

void VideoStreamWindow::showEvent(QShowEvent *event)
{
    Q_UNUSED(event);
    setWindowTitle(tr("Video Stream"));
    resize(600, 600);
}

void VideoStreamWindow::resizeEvent(QResizeEvent *event)
{
    Q_UNUSED(event);
}

void VideoStreamWindow::closeEvent(QCloseEvent *event)
{
    Q_UNUSED(event);
    m_frameLabel->setText(tr("Not started ..."));
    m_imageLabel->setPixmap(QPixmap());
    emit videoStreamWindowAboutToGetClosed();
}

void VideoStreamWindow::updateFrameImage(const QImage& frameImage, size_t frameId)
{
    Q_UNUSED(frameImage);
    m_frameLabel->setText(QString(u"ID: %1"_s).arg(QString::number(frameId)));
    m_imageLabel->setPixmap(QPixmap::fromImage(frameImage));
}
