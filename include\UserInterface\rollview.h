#ifndef ROLLVIEW_H
#define ROLLVIEW_H

#include <QWidget>
#include <QLayout>
#include <QPointer>
#include <QMouseEvent>
#include <QShowEvent>
#include <QTransform>
#include <QResizeEvent>

#include "spdlog/spdlog.h"

#include "scenemanager.h"

QT_BEGIN_NAMESPACE
namespace Ui {
class RollView;
}
QT_END_NAMESPACE

class RollView : public QWidget
{
    Q_OBJECT
public:
    explicit RollView(QWidget *parent = nullptr);
    ~RollView();

    void initialize(SceneManager::CameraPosition position, SceneManager *sceneManager);

    bool eventFilter(QObject *obj, QEvent *event) override;
    void showEvent(QShowEvent *event) override;
    void resizeEvent(QResizeEvent *event) override;

public slots:
    void applyRollMovement(qreal dy);
        
private slots:
    void updateRollPositionLabels();

private:
    enum ScrollBehavior {
        KeepScrollPosition = 0,
        ScrollToTop,
        ScrollToBottom
    };

    enum SubRollLayout {
        SubRollIdLayout,
        SubRollAggregationLayout
    };

    Ui::RollView *ui;

    bool checkSceneManager();
    void resetLayout(QLayout* layout) const;
    void updateViewport(ScrollBehavior scrollBehavior = ScrollBehavior::KeepScrollPosition);

    // methods to update info labels
    void initializeRollIds();
    void initializeDefectAggregationLabels();
    void initializeSubRollLayout(SubRollLayout subRollLayout);
    void updateDefectAggregationLabels(int subRollNr, int totalDetections);


    SceneManager::CameraPosition m_cameraPosition { SceneManager::UnknownCameraPosition };

    QPointer<SceneManager> m_sceneManager;
    bool m_isinitialized { false };

    std::shared_ptr<spdlog::logger> m_logger;
};
#endif // ROLLVIEW_H
