# QtConfig.cmake - Common Qt configuration

# Find Qt packages
list(APPEND CMAKE_PREFIX_PATH "$ENV{QTDIR}")
find_package(Qt6 COMPONENTS Core Widgets REQUIRED)
if(Qt6_FOUND)
    message(STATUS "Found Qt6: ${Qt6_VERSION}")
    
    # Enable Qt features
    set(CMAKE_AUTOMOC ON)
    set(CMAKE_AUTORCC ON)
    set(CMAKE_AUTOUIC ON)
    
    # Define a comprehensive helper function to deploy Qt for Windows
    function(deploy_qt_for_target TARGET_NAME)
        # Parse additional arguments
        cmake_parse_arguments(DEPLOY "" "" "TRANSLATIONS;ADDITIONAL_BINARIES" ${ARGN})
        
        if(WIN32)
            # Find windeployqt
            find_program(WINDEPLOYQT_EXECUTABLE windeployqt HINTS "$ENV{QTDIR}/bin" "${Qt6_DIR}/../../../bin")
            if(NOT WINDEPLOYQT_EXECUTABLE)
                message(WARNING "windeployqt not found. Ensure Qt is installed and QTDIR is set.")
            else()
                # Add post-build command to deploy Qt dependencies for the main target
                add_custom_command(TARGET ${TARGET_NAME} POST_BUILD
                    COMMAND "${WINDEPLOYQT_EXECUTABLE}" 
                            --verbose 1
                            --translations
                            --no-compiler-runtime
                            --no-system-d3d-compiler
                            --dir "$<TARGET_FILE_DIR:${TARGET_NAME}>"
                            "$<TARGET_FILE:${TARGET_NAME}>"
                    WORKING_DIRECTORY "$<TARGET_FILE_DIR:${TARGET_NAME}>"
                    COMMENT "Deploying Qt dependencies with windeployqt for ${TARGET_NAME}"
                )
                
                # Deploy Qt dependencies for additional binaries (DLLs)
                if(DEPLOY_ADDITIONAL_BINARIES)
                    foreach(BINARY ${DEPLOY_ADDITIONAL_BINARIES})
                        add_custom_command(TARGET ${TARGET_NAME} POST_BUILD
                            COMMAND "${WINDEPLOYQT_EXECUTABLE}" 
                                    --verbose 1
                                    --translations
                                    --no-compiler-runtime
                                    --no-system-d3d-compiler
                                    --dir "$<TARGET_FILE_DIR:${TARGET_NAME}>"
                                    "${BINARY}"
                            WORKING_DIRECTORY "$<TARGET_FILE_DIR:${TARGET_NAME}>"
                            COMMENT "Deploying Qt dependencies with windeployqt for ${BINARY}"
                        )
                    endforeach()
                endif()
                
                # Manually copy additional Qt components that might be missed by windeployqt
                # Get Qt installation directory
                get_target_property(QT_QMAKE_EXECUTABLE Qt6::qmake IMPORTED_LOCATION)
                get_filename_component(QT_INSTALL_BIN_DIR "${QT_QMAKE_EXECUTABLE}" DIRECTORY)
                get_filename_component(QT_INSTALL_DIR "${QT_INSTALL_BIN_DIR}" DIRECTORY)
                
                # Copy translations
                set(QT_TRANSLATIONS_DIR "${QT_INSTALL_DIR}/translations")
                add_custom_command(TARGET ${TARGET_NAME} POST_BUILD
                    COMMAND ${CMAKE_COMMAND} -E make_directory "$<TARGET_FILE_DIR:${TARGET_NAME}>/translations"
                    COMMAND ${CMAKE_COMMAND} -E copy_directory "${QT_TRANSLATIONS_DIR}" "$<TARGET_FILE_DIR:${TARGET_NAME}>/translations"
                    COMMENT "Copying Qt translations for ${TARGET_NAME}"
                )
                
                # Copy custom translation files if provided
                if(DEPLOY_TRANSLATIONS)
                    foreach(TRANSLATION_FILE ${DEPLOY_TRANSLATIONS})
                        get_filename_component(TRANSLATION_FILENAME ${TRANSLATION_FILE} NAME)
                        add_custom_command(TARGET ${TARGET_NAME} POST_BUILD
                            COMMAND ${CMAKE_COMMAND} -E copy 
                                    "${TRANSLATION_FILE}" 
                                    "$<TARGET_FILE_DIR:${TARGET_NAME}>/translations/${TRANSLATION_FILENAME}"
                            COMMENT "Copying custom translation file: ${TRANSLATION_FILENAME}"
                        )
                    endforeach()
                endif()
                
                # Copy styles directly to the build folder
                set(QT_STYLES_DIR "${QT_INSTALL_DIR}/plugins/styles")
                add_custom_command(TARGET ${TARGET_NAME} POST_BUILD
                    COMMAND ${CMAKE_COMMAND} -E copy_directory 
                            "${QT_STYLES_DIR}" 
                            "$<TARGET_FILE_DIR:${TARGET_NAME}>"
                    COMMENT "Copying Qt styles for ${TARGET_NAME}"
                )
                
                # Copy platforms directly to the build folder
                set(QT_PLATFORMS_DIR "${QT_INSTALL_DIR}/plugins/platforms")
                add_custom_command(TARGET ${TARGET_NAME} POST_BUILD
                    COMMAND ${CMAKE_COMMAND} -E copy_directory 
                            "${QT_PLATFORMS_DIR}" 
                            "$<TARGET_FILE_DIR:${TARGET_NAME}>"
                    COMMENT "Copying Qt platforms for ${TARGET_NAME}"
                )
                
                # Copy imageformats directly to the build folder
                set(QT_IMAGEFORMATS_DIR "${QT_INSTALL_DIR}/plugins/imageformats")
                add_custom_command(TARGET ${TARGET_NAME} POST_BUILD
                    COMMAND ${CMAKE_COMMAND} -E copy_directory 
                            "${QT_IMAGEFORMATS_DIR}" 
                            "$<TARGET_FILE_DIR:${TARGET_NAME}>"
                    COMMENT "Copying Qt imageformats for ${TARGET_NAME}"
                )
                
                # # Create qt.conf file to point to the current directory for plugins
                # file(GENERATE OUTPUT "$<TARGET_FILE_DIR:${TARGET_NAME}>/qt.conf" 
                #      CONTENT "[Paths]\nPlugins = .\nTranslations = ./translations\n")
            endif()
        endif()
    endfunction()
else()
    message(FATAL_ERROR "Qt6 not found. Please install Qt6 or set Qt6_DIR.")
endif()




