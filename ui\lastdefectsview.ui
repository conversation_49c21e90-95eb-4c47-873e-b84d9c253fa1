<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>LastDefectsView</class>
 <widget class="QListWidget" name="LastDefectsView">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1241</width>
    <height>942</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <property name="flow">
   <enum>QListView::Flow::LeftToRight</enum>
  </property>
  <property name="isWrapping" stdset="0">
   <bool>true</bool>
  </property>
  <property name="resizeMode">
   <enum>QListView::ResizeMode::Fixed</enum>
  </property>
  <property name="uniformItemSizes">
   <bool>false</bool>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <property name="spacing">
    <number>0</number>
   </property>
   <property name="leftMargin">
    <number>4</number>
   </property>
   <property name="topMargin">
    <number>0</number>
   </property>
   <property name="rightMargin">
    <number>4</number>
   </property>
   <property name="bottomMargin">
    <number>0</number>
   </property>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
