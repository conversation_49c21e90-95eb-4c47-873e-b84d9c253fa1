<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>RollView</class>
 <widget class="QWidget" name="RollView">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>638</width>
    <height>704</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout_3">
   <property name="spacing">
    <number>0</number>
   </property>
   <property name="leftMargin">
    <number>16</number>
   </property>
   <property name="topMargin">
    <number>4</number>
   </property>
   <property name="rightMargin">
    <number>16</number>
   </property>
   <property name="bottomMargin">
    <number>4</number>
   </property>
   <item>
    <widget class="QLabel" name="rollViewCurrentCameraLabel">
     <property name="font">
      <font>
       <pointsize>12</pointsize>
       <fontweight>DemiBold</fontweight>
      </font>
     </property>
     <property name="text">
      <string>Roll Map</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignmentFlag::AlignCenter</set>
     </property>
    </widget>
   </item>
   <item>
    <widget class="QWidget" name="spacing1" native="true">
     <property name="minimumSize">
      <size>
       <width>0</width>
       <height>8</height>
      </size>
     </property>
    </widget>
   </item>
   <item>
    <layout class="QHBoxLayout" name="upperXAxis">
     <item>
      <widget class="QLabel" name="totalsLabel">
       <property name="minimumSize">
        <size>
         <width>80</width>
         <height>0</height>
        </size>
       </property>
       <property name="maximumSize">
        <size>
         <width>80</width>
         <height>16777215</height>
        </size>
       </property>
       <property name="baseSize">
        <size>
         <width>71</width>
         <height>0</height>
        </size>
       </property>
       <property name="font">
        <font>
         <pointsize>14</pointsize>
         <fontweight>DemiBold</fontweight>
        </font>
       </property>
       <property name="text">
        <string>Totals</string>
       </property>
       <property name="alignment">
        <set>Qt::AlignmentFlag::AlignRight|Qt::AlignmentFlag::AlignTrailing|Qt::AlignmentFlag::AlignVCenter</set>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QWidget" name="totalRollDefectWidget" native="true"/>
     </item>
    </layout>
   </item>
   <item>
    <layout class="QHBoxLayout" name="mainLayout">
     <property name="spacing">
      <number>0</number>
     </property>
     <item>
      <widget class="QWidget" name="yAxisWidget" native="true">
       <property name="sizePolicy">
        <sizepolicy hsizetype="Fixed" vsizetype="Preferred">
         <horstretch>0</horstretch>
         <verstretch>0</verstretch>
        </sizepolicy>
       </property>
       <property name="minimumSize">
        <size>
         <width>80</width>
         <height>0</height>
        </size>
       </property>
       <property name="maximumSize">
        <size>
         <width>80</width>
         <height>16777215</height>
        </size>
       </property>
       <property name="baseSize">
        <size>
         <width>80</width>
         <height>0</height>
        </size>
       </property>
       <layout class="QVBoxLayout" name="verticalLayout">
        <property name="spacing">
         <number>0</number>
        </property>
        <property name="leftMargin">
         <number>0</number>
        </property>
        <property name="topMargin">
         <number>0</number>
        </property>
        <property name="rightMargin">
         <number>4</number>
        </property>
        <property name="bottomMargin">
         <number>0</number>
        </property>
        <item>
         <widget class="QLabel" name="upperRollPosYLabel">
          <property name="font">
           <font>
            <pointsize>14</pointsize>
            <fontweight>DemiBold</fontweight>
           </font>
          </property>
          <property name="text">
           <string>Pos1</string>
          </property>
          <property name="alignment">
           <set>Qt::AlignmentFlag::AlignRight|Qt::AlignmentFlag::AlignTop|Qt::AlignmentFlag::AlignTrailing</set>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QLabel" name="centerRollPosYLabel">
          <property name="font">
           <font>
            <pointsize>14</pointsize>
            <fontweight>DemiBold</fontweight>
           </font>
          </property>
          <property name="text">
           <string>Pos2</string>
          </property>
          <property name="alignment">
           <set>Qt::AlignmentFlag::AlignRight|Qt::AlignmentFlag::AlignTrailing|Qt::AlignmentFlag::AlignVCenter</set>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QLabel" name="lowerRollPosYLabel">
          <property name="font">
           <font>
            <pointsize>14</pointsize>
            <fontweight>DemiBold</fontweight>
           </font>
          </property>
          <property name="text">
           <string>Pos3</string>
          </property>
          <property name="alignment">
           <set>Qt::AlignmentFlag::AlignBottom|Qt::AlignmentFlag::AlignRight|Qt::AlignmentFlag::AlignTrailing</set>
          </property>
         </widget>
        </item>
       </layout>
      </widget>
     </item>
     <item>
      <widget class="QGraphicsView" name="rollViewWidget">
       <property name="minimumSize">
        <size>
         <width>0</width>
         <height>600</height>
        </size>
       </property>
       <property name="maximumSize">
        <size>
         <width>16777215</width>
         <height>600</height>
        </size>
       </property>
       <property name="baseSize">
        <size>
         <width>0</width>
         <height>600</height>
        </size>
       </property>
      </widget>
     </item>
    </layout>
   </item>
   <item>
    <layout class="QHBoxLayout" name="lowerXAxis">
     <property name="spacing">
      <number>0</number>
     </property>
     <item>
      <widget class="QWidget" name="rollNrWidget" native="true">
       <property name="minimumSize">
        <size>
         <width>0</width>
         <height>0</height>
        </size>
       </property>
       <property name="maximumSize">
        <size>
         <width>16777215</width>
         <height>16777215</height>
        </size>
       </property>
       <property name="baseSize">
        <size>
         <width>0</width>
         <height>0</height>
        </size>
       </property>
      </widget>
     </item>
    </layout>
   </item>
   <item>
    <widget class="QWidget" name="spacing2" native="true">
     <property name="minimumSize">
      <size>
       <width>0</width>
       <height>8</height>
      </size>
     </property>
    </widget>
   </item>
   <item>
    <widget class="QLabel" name="crosswebLabel">
     <property name="font">
      <font>
       <pointsize>12</pointsize>
      </font>
     </property>
     <property name="text">
      <string>DS &lt;---- Crossweb ----&gt; OS</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignmentFlag::AlignCenter</set>
     </property>
    </widget>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
