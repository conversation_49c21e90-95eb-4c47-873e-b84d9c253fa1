#include "rollview.h"
#include "rollscene.h"
#include "ui_rollview.h"

#include <QEvent>
#include <QTimer>
#include <QFont>
#include <QGraphicsScene>
#include <QGraphicsView>
#include <QHBoxLayout>
#include <QLabel>
#include <QLayoutItem>
#include <QPointF>
#include <QScrollBar>
#include <QString>
#include <QWidget>

#include <cmath>

#include "spdlog/sinks/stdout_color_sinks.h"

using namespace Qt::StringLiterals;

RollView::RollView(QWidget* parent)
    : QWidget(parent)
    , ui(new Ui::RollView)
{
    ui->setupUi(this);
    m_logger = spdlog::get("UserInterfaceApplication");
    if (!m_logger) {
        m_logger = spdlog::stdout_color_mt("UserInterfaceApplication");
        m_logger->set_level(spdlog::level::debug);
    }
    m_logger->info("Roll view created ...");
}

RollView::~RollView()
{
    delete ui;
}

void RollView::initialize(SceneManager::CameraPosition position, SceneManager* sceneManager)
{
    if (!m_sceneManager.isNull()) {
        m_logger->warn("RollView: SceneManager already set; Cannot initialize multiple SceneManagers");
        return;
    }

    if (sceneManager == nullptr || !sceneManager->isInitialized(position)) {
        m_logger->warn("RollView: SceneManager not active");
        return;
    }

    if (position == SceneManager::UnknownCameraPosition) {
        m_logger->warn("RollView: Cannot set camera position to UnknownCameraPosition");
        return;
    }

    if (position == SceneManager::TopCameraView) {
        ui->rollViewCurrentCameraLabel->setText(QObject::tr("Top Camera"));
    } else if (position == SceneManager::BottomCameraView) {
        ui->rollViewCurrentCameraLabel->setText(QObject::tr("Bottom Camera"));
    }

    m_cameraPosition = position;
    m_sceneManager = sceneManager;

    connect(m_sceneManager, &SceneManager::rollMoved, this, &RollView::applyRollMovement);
    connect(m_sceneManager, &SceneManager::aggregationChanged,
        this, [this, position](SceneManager::CameraPosition cameraPosition, int subRollNr, int totalDetections) {
            if (cameraPosition == position) {
                this->updateDefectAggregationLabels(subRollNr, totalDetections);
            }
        });

    auto view = ui->rollViewWidget;
    view->setVerticalScrollBarPolicy(Qt::ScrollBarAlwaysOn);
    view->setHorizontalScrollBarPolicy(Qt::ScrollBarAlwaysOff);
    view->setAlignment(Qt::AlignLeft | Qt::AlignTop);
    view->installEventFilter(this);

    m_isinitialized = true;
}

bool RollView::eventFilter(QObject* obj, QEvent* event)
{
    if (event->type() == QEvent::MouseButtonPress) {
        auto mEvent = static_cast<QMouseEvent*>(event);
        auto view = ui->rollViewWidget;
        auto x = mEvent->position().x();
        auto y = mEvent->position().y();
        auto mapped = view->mapToScene(x, y);
        m_logger->info("MOUSE POS: ({}, {}) with height {}", x, y, view->height());
        m_logger->info("MAPPED POS: ({}, {})", mapped.x(), mapped.y());
        return true;
    }
    return QObject::eventFilter(obj, event);
}

void RollView::showEvent(QShowEvent* event)
{
    Q_UNUSED(event);
    updateViewport(ScrollBehavior::ScrollToBottom);
}

void RollView::resizeEvent(QResizeEvent* event)
{
    Q_UNUSED(event);
    updateViewport(ScrollBehavior::KeepScrollPosition);
}

void RollView::updateViewport(ScrollBehavior scrollBehavior)
{
    if (!m_isinitialized) {
        return;
    }

    auto view = ui->rollViewWidget;
    view->setScene(m_sceneManager->sceneForCamera(m_cameraPosition));
    auto viewport = m_sceneManager->cameraViewport(m_cameraPosition);

    auto viewTransform = view->transform();
    viewTransform.reset();

    // roll moves from top to bottom, meaning we have to mirror
    viewTransform.scale(1.0, -1.0);
    viewTransform.translate(-viewport.x(), -viewport.y() - viewport.height());
    viewTransform.scale((view->width() - view->verticalScrollBar()->width()) / viewport.width(),
                         view->height() / viewport.height());

    view->setTransform(viewTransform);
    connect(view->verticalScrollBar(), &QScrollBar::valueChanged, this, &RollView::updateRollPositionLabels);

    if (scrollBehavior == ScrollBehavior::ScrollToTop) {
        view->verticalScrollBar()->setValue(0.0);
    } else if (scrollBehavior == ScrollBehavior::ScrollToBottom) {
        applyRollMovement(0.0);
    }

    // init axis labels
    initializeRollIds();
    initializeDefectAggregationLabels();
}

void RollView::applyRollMovement(qreal dy)
{
    auto view = ui->rollViewWidget;
    auto scene = view->scene();

    RollScene* rollScene = nullptr;
    if ((rollScene = qobject_cast<RollScene*>(scene)) != nullptr) {
        rollScene->moveInspectorLine(dy);
        auto inspectorLineCenter = rollScene->centerOfInspectorLine();
        view->centerOn(inspectorLineCenter);
    } else {
        // no inspector line found: scroll directly by dy
        auto viewTransform = view->transform();
        auto currentScrollPos = view->verticalScrollBar()->value();

        auto pointInViewSpace = viewTransform.map(QPointF(0.0, dy));
        view->verticalScrollBar()->setValue(currentScrollPos + pointInViewSpace.y());
    }
}

bool RollView::checkSceneManager()
{
    if (m_sceneManager.isNull()) {
        m_logger->warn("SceneManager is not active. Cannot set roll data");
        return false;
    }

    if (!m_sceneManager->isInitialized(m_cameraPosition)) {
        m_logger->warn("SceneManager not active for camera position {}", static_cast<int>(m_cameraPosition));
        return false;
    }
    return true;
}

void RollView::resetLayout(QLayout* layout) const
{
    if (layout) {
        QLayoutItem* child = nullptr;
        while ((child = layout->takeAt(0)) != nullptr) {
            if (QWidget* w = child->widget()) {
                w->setParent(nullptr);
                delete w;
            }
            delete child;
        }
    }
}

void RollView::initializeRollIds()
{
    initializeSubRollLayout(RollView::SubRollIdLayout);
}

void RollView::initializeDefectAggregationLabels()
{
    initializeSubRollLayout(RollView::SubRollAggregationLayout);
}

void RollView::initializeSubRollLayout(RollView::SubRollLayout subRollLayout)
{
    if (!checkSceneManager()) {
        return;
    }

    auto view = ui->rollViewWidget;
    auto viewTransform = view->transform();

    auto widget = subRollLayout == RollView::SubRollIdLayout ? ui->rollNrWidget : ui->totalRollDefectWidget;

    if (subRollLayout == RollView::SubRollIdLayout) {
        auto xAxisLayout = ui->lowerXAxis->layout();
        if (xAxisLayout) {
            xAxisLayout->setAlignment(widget, Qt::AlignRight | Qt::AlignTop);
        }
    }

    QHBoxLayout* layout = qobject_cast<QHBoxLayout*>(widget->layout());
    if (layout) {
        resetLayout(layout);
    } else {
        layout = new QHBoxLayout(widget);
    }
    layout->setSpacing(0);
    layout->setContentsMargins(0, 0, 0, 0);

    auto boldFont = font();
    boldFont.setWeight(QFont::DemiBold);
    boldFont.setPointSize(14);

    const auto subRolls = m_sceneManager->subRolls();
    auto leftBorderWidth = viewTransform.map(QPointF(m_sceneManager->leftBorderWidth(), 0.0)).x();
    auto rightBorderWidth = viewTransform.map(QPointF(m_sceneManager->rightBorderWidth(), 0.0)).x();
    auto finalProductWidth = m_sceneManager->finalProductWidth();
    auto subRollWidth = viewTransform.map(QPointF(finalProductWidth / subRolls.count(), 0.0)).x();

    auto leftBorderLabel = new QLabel(u""_s);
    // due to some internal layouting heuristics by Qt, we have to set maximum width on
    // the upper x axis to be able to shrink the main window after expanding it
    // Note: the upper x axis widget is wrapped inside another QHBoxLayout
    // Change the code, if you change layout structure in the ui file/Qt Designer
    if (subRollLayout == RollView::SubRollIdLayout) {
        leftBorderLabel->setFixedWidth(leftBorderWidth);
    } else {
        leftBorderLabel->setMaximumWidth(leftBorderWidth);
    }
    leftBorderLabel->setObjectName(u"leftBorderLabel"_s);
    layout->addWidget(leftBorderLabel);

    for (const auto& subRoll : subRolls) {
        QString value;
        auto labelId = QString(u"subRoll_%1"_s).arg(subRoll.subRollId);
        if (subRollLayout == RollView::SubRollIdLayout) {
            value = QString::number(subRoll.subRollId);
        } else {
            if (m_cameraPosition == SceneManager::TopCameraView) {
                value = QString::number(subRoll.topCameraDetections);
            } else {
                value = QString::number(subRoll.bottomCameraDetections);
            }
        }
        auto axisLabel = new QLabel(value);
        // When we update the label text, we search for the label with the fitting object name!
        axisLabel->setObjectName(labelId);
        // see coment above
        if (subRollLayout == RollView::SubRollIdLayout) {
            axisLabel->setFixedWidth(subRollWidth);
        } else {
            axisLabel->setMaximumWidth(subRollWidth);
        }
        axisLabel->setAlignment(Qt::AlignHCenter | Qt::AlignTop);
        axisLabel->setFont(boldFont);
        layout->addWidget(axisLabel);
    }

    auto rightBorderLabel = new QLabel(u""_s);
    // see coment above
    if (subRollLayout == RollView::SubRollIdLayout) {
        rightBorderLabel->setFixedWidth(rightBorderWidth + view->verticalScrollBar()->width());
    } else {
        rightBorderLabel->setMaximumWidth(rightBorderWidth + view->verticalScrollBar()->width());
    }
    rightBorderLabel->setObjectName(u"rightBorderLabel"_s);
    layout->addWidget(rightBorderLabel);
}

void RollView::updateDefectAggregationLabels(int subRollNr, int totalDetections)
{
    auto widget = ui->totalRollDefectWidget;
    auto layout = widget->layout();
    auto labelIdToSearchFor = QString(u"subRoll_%1"_s).arg(subRollNr);

    if (!layout) {
        m_logger->warn("Cannot update roll aggregations: No layout set");
        return;
    }

    for (auto idx = 0; idx < layout->count(); idx++) {
        if (auto label = qobject_cast<QLabel*>(layout->itemAt(idx)->widget())) {
            if (label->objectName() == labelIdToSearchFor) {
                label->setText(QString::number(totalDetections));
                break;
            }
        }
    }
}

void RollView::updateRollPositionLabels()
{
    auto view = ui->rollViewWidget;

    const auto viewHeight = view->height();
    const auto upperPoint = QPoint(0, 0);
    const auto centerPoint = QPoint(0, static_cast<int>(std::floor(viewHeight * 0.5)));
    const auto bottomPoint = QPoint(0, viewHeight);

    const auto scenePositionTop = view->mapToScene(upperPoint);
    const auto scenePositionCenter = view->mapToScene(centerPoint);
    const auto scenePositionBottom = view->mapToScene(bottomPoint);

    const qreal cmToM = 1.0 / 100.0;
    ui->upperRollPosYLabel->setText(QString::number(scenePositionTop.y() * cmToM, 'f', 2));
    ui->centerRollPosYLabel->setText(QString::number(scenePositionCenter.y() * cmToM, 'f', 2));
    ui->lowerRollPosYLabel->setText(QString::number(scenePositionBottom.y() * cmToM, 'f', 2));
}
