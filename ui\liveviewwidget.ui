<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>LiveViewWidget</class>
 <widget class="QWidget" name="LiveViewWidget">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1241</width>
    <height>942</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <property name="spacing">
    <number>0</number>
   </property>
   <property name="leftMargin">
    <number>4</number>
   </property>
   <property name="topMargin">
    <number>0</number>
   </property>
   <property name="rightMargin">
    <number>4</number>
   </property>
   <property name="bottomMargin">
    <number>0</number>
   </property>
   <item>
    <widget class="QSplitter" name="mainSplitter">
     <property name="orientation">
      <enum>Qt::Orientation::Horizontal</enum>
     </property>
     <property name="opaqueResize">
      <bool>false</bool>
     </property>
     <widget class="QWidget" name="leftView" native="true">
      <layout class="QVBoxLayout" name="verticalLayout_2">
       <item>
        <widget class="QSplitter" name="leftViewSplitter">
         <property name="orientation">
          <enum>Qt::Orientation::Vertical</enum>
         </property>
         <widget class="RollView" name="topCameraView" native="true"/>
         <widget class="LastDefectsView" name="topCameraRecentDetections" native="true"/>
        </widget>
       </item>
      </layout>
     </widget>
     <widget class="QWidget" name="rightView" native="true">
      <layout class="QVBoxLayout" name="verticalLayout_3">
       <item>
        <widget class="QSplitter" name="rightViewSplitter">
         <property name="orientation">
          <enum>Qt::Orientation::Vertical</enum>
         </property>
         <widget class="RollView" name="bottomCameraView" native="true"/>
         <widget class="LastDefectsView" name="bottomCameraRecentDetections" native="true"/>
        </widget>
       </item>
      </layout>
     </widget>
    </widget>
   </item>
  </layout>
 </widget>
 <customwidgets>
  <customwidget>
   <class>RollView</class>
   <extends>QWidget</extends>
   <header>rollview.h</header>
   <container>1</container>
  </customwidget>
  <customwidget>
   <class>LastDefectsView</class>
   <extends>QWidget</extends>
   <header>lastdefectsview.h</header>
   <container>1</container>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections/>
</ui>
