#include "lastdefectsview.h"
#include "ui_lastdefectsview.h"

#include <QObject>
#include <QVariant>
#include <QSharedPointer>
#include <QShowEvent>
#include <QResizeEvent>
#include <QImage>
#include <QScrollBar>
#include <QFont>
#include <QTransposeProxyModel>
#include <QApplication>
#include <QListWidgetItem>

#include "spdlog/sinks/stdout_color_sinks.h"
#include "detectionmodel.h"
#include "defectitem.h"

using namespace Qt::StringLiterals;

class DetectionTransposeProxyModel : public QTransposeProxyModel
{
public:
    DetectionTransposeProxyModel(QObject *parent = nullptr)
        : QTransposeProxyModel(parent) {}

    ~DetectionTransposeProxyModel() = default;

    QVariant data(const QModelIndex &index, int role = Qt::DisplayRole) const override
    {
        if (!checkIndex(index)) {
            return QVariant();
        }


        if (role == Qt::DisplayRole) {
            auto value = QTransposeProxyModel::data(index, role);
            // Note: columns and rows are switched!
            if (index.row() == DetectionModel::BoundingRect && value.canConvert<QRectF>()) {
                const auto rect = value.toRectF();
                const qreal cmToM = 1.0 / 100.0;
                auto xx = QString::number(rect.x() * cmToM, 'f', 2);
                auto yy = QString::number(rect.y() * cmToM, 'f', 2);
                auto ww = QString::number(rect.width() * cmToM, 'f', 2);
                auto hh = QString::number(rect.height() * cmToM, 'f', 2);
                value = u"%1, %2 (%3 X %4)"_s.arg(xx, yy, ww, hh);
            }
            switch(index.row()) {
                case DetectionModel::Id:
                return u"%1:\t %2"_s.arg(tr("Id"), value.toString());
                case DetectionModel::FailureClass:
                    return u"%1:\t %2"_s.arg(tr("Failure Class"), value.toString());
                case DetectionModel::FailureString:
                    return u"%1:\t %2"_s.arg(tr("Failure Name"), value.toString());
                case DetectionModel::BoundingRect:
                    return u"%1:\t %2"_s.arg(tr("Bounding Rect"), value.toString());
                case DetectionModel::ConfidenceScore:
                    return u"%1:\t %2"_s.arg(tr("Confidence Score"), value.toString());
                case DetectionModel::DetectionImage:
                    return value;
                case DetectionModel::AffectedSubRolls:
                    return u"%1:\t %2"_s.arg(tr("Affected Subrolls"), value.toString());
                default: return QVariant();
            }
        } else if(role == Qt::FontRole) {
            auto f = QApplication::font();
            f.setWeight(QFont::DemiBold);
            f.setPointSize(10);
            return f;
        }

        return QTransposeProxyModel::data(index, role);
    }
};

LastDefectsView::LastDefectsView(QWidget *parent)
    : QListWidget(parent)
    , ui(new Ui::LastDefectsView)
    , m_listModel(new DetectionTransposeProxyModel(this))
{
    m_logger = spdlog::get("UserInterfaceApplication");
    if (!m_logger) {
        m_logger = spdlog::stdout_color_mt("UserInterfaceApplication");
        m_logger->set_level(spdlog::level::debug);
    }
    ui->setupUi(this);
    initializeUI();
}

LastDefectsView::~LastDefectsView()
{
    delete ui;
}

void LastDefectsView::initializeUI()
{
    setVerticalScrollBarPolicy(Qt::ScrollBarAlwaysOn);
    setHorizontalScrollBarPolicy(Qt::ScrollBarAlwaysOff);
    setGridSize(QSize());
}

void LastDefectsView::showEvent(QShowEvent* event)
{
    Q_UNUSED(event);
    resetGridSize();
}

void LastDefectsView::resizeEvent(QResizeEvent* event)
{
    Q_UNUSED(event);
    resetGridSize();
}

void LastDefectsView::resetGridSize()
{
    const int margins = 8 * 2;
    m_gridSize = QSize(width() * 0.5 - margins, height() - margins);
    setGridSize(m_gridSize);
}

void LastDefectsView::initialize(SceneManager::CameraPosition position, SceneManager* sceneManager)
{
    if (!m_sceneManager.isNull()) {
        m_logger->warn("LastDefectView: SceneManager already set; Cannot initialize multiple SceneManagers");
        return;
    }

    if (sceneManager == nullptr || !sceneManager->isInitialized(position)) {
        m_logger->warn("LastDefectView: SceneManager not active");
        return;
    }

    if (position == SceneManager::UnknownCameraPosition) {
        m_logger->warn("LastDefectView: Cannot set camera position to UnknownCameraPosition");
        return;
    }

    m_cameraPosition = position;
    m_sceneManager = sceneManager;

    QSharedPointer<DetectionModel> detectionModel = m_sceneManager->detectionModel(m_cameraPosition);
    if (detectionModel.isNull()) {
        m_logger->warn("LastDefectView: SceneManager has no detection model");
        return;
    }

    m_listModel->setSourceModel(detectionModel.data());

    connect(detectionModel.data(), &QAbstractItemModel::rowsInserted,
            this, [this](const QModelIndex &, int first, int last) {
                for (auto idx = first; idx <= last; idx++) {
                    this->addDetectionToView(idx);
                }
            });
}

void LastDefectsView::addDetectionToView(int row)
{
    auto detectionModel = m_listModel->sourceModel();
    if (!detectionModel) {
        return;
    }

    const auto detectionId = detectionModel->data(detectionModel->index(row, DetectionModel::Id), Qt::DisplayRole).toString();
    const auto failureType = detectionModel->data(detectionModel->index(row, DetectionModel::FailureClass), Qt::DisplayRole).toInt();
    const auto detectionImageVariant = detectionModel->data(detectionModel->index(row, DetectionModel::DetectionImage), Qt::DisplayRole);

    QImage detectionImage;
    if (detectionImageVariant.canConvert<QImage>()) {
        detectionImage = detectionImageVariant.value<QImage>();
    }

    auto listWidgetItem = new QListWidgetItem(this);
    auto defectItem = new DefectItem(this);
    defectItem->setDetectionInfoModel(m_listModel);
    defectItem->setDetectionNumber(row);
    defectItem->setDetectionId(detectionId);
    defectItem->setFailureType(static_cast<DetectionModel::FailureType>(failureType));
    defectItem->setDetectionImage(detectionImage);

    if (m_gridSize.isValid()) {
        listWidgetItem->setSizeHint(m_gridSize);
    }
    setItemWidget(listWidgetItem, defectItem);
    addItem(listWidgetItem);

    // scroll to bottom
    verticalScrollBar()->setValue(verticalScrollBar()->maximum());
}








