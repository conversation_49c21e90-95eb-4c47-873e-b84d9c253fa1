#ifndef DETECTION_SIMULATOR
#define DETECTION_SIMULATOR

#include <QPointer>
#include <QTimer>
#include <QImage>
#include <QRandomGenerator>

#include "scenemanager.h"

class DetectionSimulator
{

public:

    struct SimulatorConfiguration {
        qreal speedInMeterPerMinute = 0.0;
        qreal defectProbability = 0.33;
        bool useDeterministicRandomGenerator = false;
        // seed value is only used for deterministic random generators
        quint32 seedValue = 1;
    };

    explicit DetectionSimulator(const SimulatorConfiguration &config, SceneManager *sceneManager);
    ~DetectionSimulator() = default;

    void start();
    void stop();

protected:
    virtual void step();

private:
    QImage pickRandomImage(QRandomGenerator& rnd) const;

    QPointer<SceneManager> m_sceneManager;

    QTimer m_timer;
    QRandomGenerator m_randomGenerator;

    // internally, we are using cm instead of meter
    qreal m_speedInCMPerSecond {0.0};
    qreal m_defectProbability {0.33};

};
#endif // DETECTION_SIMULATOR
