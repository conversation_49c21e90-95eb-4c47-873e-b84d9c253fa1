#define _WIN_SOCKAPI_  // Needed to avoid winsock.h inclusion.
#include <winsock2.h>
#include <ws2tcpip.h>

#include "PrepregManager.h"
// #include "Logger.h"

#include <iostream>
#include <memory>
#include <thread>
#include <chrono>


int main(/*int argc, char** argv*/)
{
#ifdef _WIN32
    // Allocate a console for this application
    AllocConsole();
    // Redirect stdout and stderr to the console
    FILE* pConsole;
    freopen_s(&pConsole, "CONOUT$", "w", stdout);
    freopen_s(&pConsole, "CONOUT$", "w", stderr);
#endif

    // HVIS::Logger::initHVISSuite();
    // spdlog::get("HVIS_Suite")->info("HVIS Suite loggers started");

    PrepregManager manager;
    manager.runImageAcquisition();
    // return manager.show();
    return 0;
}