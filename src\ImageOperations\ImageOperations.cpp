#include "ImageOperations.h"
#include <iostream>
#include <cuda_runtime.h>


void splitImageCUDA(
    uint8_t* gpuPtr, 
    int width,
    int height,
    int channels,
    int type,
    int step,
    int tileWidth,
    int tileHeight
) {
    std::vector<cv::cuda::GpuMat> tiles;

    // Wrap the raw GPU pointer in a GpuMat with explicit step
    cv::cuda::GpuMat d_image(height, width, CV_8UC3, gpuPtr, step);

    if (d_image.empty())
    {
        std::cerr << "Failed to create GpuMat from raw pointer!" << std::endl;
        return;
    }

    // Sanity check to get the image back
    // Allocate host Mat for output
    cv::Mat h_image(height, width, CV_8UC3);
    std::cout << h_image.step << std::endl;

    // Copy from page-locked memory to host Mat
    for (int y = 0; y < height; ++y)
    {
        unsigned char* src_row = d_image.ptr<unsigned char>(y);
        unsigned char* dst_row = h_image.data + y * h_image.step;
        
        cudaMemcpy(dst_row, src_row, width * 3, cudaMemcpyHostToHost);
    }

    cv::imshow("Sanity Check", h_image);
    cv::waitKey(0);

    exit(0);

    int numTilesX = width / tileWidth;
    int numTilesY = height / tileHeight;

    for (int row = 0; row < numTilesY; ++row)
    {
        for (int col = 0; col < numTilesX; ++col)
        {
            cv::Rect roi(col * tileWidth, row * tileHeight, tileWidth, tileHeight);
            tiles.push_back(d_image(roi));
        }
    }

    // return tiles;
}