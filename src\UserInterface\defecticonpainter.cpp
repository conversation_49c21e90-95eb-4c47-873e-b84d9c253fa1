#include "defecticonpainter.h"

#include <QPainter>
#include <QPen>
#include <QPoint>
#include <QBrush>
#include <QPainterPath>
#include <QFontMetrics>

#include "detectioncolors.h"

QPixmap DefectIconPainter::paintDefectItem(DetectionModel::FailureType failureType, const QSize &size)
{
    switch (failureType) {
        case DetectionModel::Development_Test_1:
            return paintEmptyPixmap(size);
        case DetectionModel::Development_Test_2:
            return paintDefectReport(size, Qt::yellow, true);
        case DetectionModel::C_Crease:
            return paintDefectReport(size, DetectionColors::C_Crease, true);
        case DetectionModel::E_Edge_Defect:
            return paintEmptyPixmap(size);
        case DetectionModel::F_Foreign_Body:
            return paintDefectReport(size, DetectionColors::F_Foreign_Body);
        case DetectionModel::J1_Prepreg_Splice:
            return paintEmptyPixmap(size);
        case DetectionModel::J2_Paper_Splice:
            return paintDefectReport(size, DetectionColors::J2_Paper_Splice, true);
        case DetectionModel::K_Gap_Birdeye:
            return paintDefectReport(size, DetectionColors::K_Gap_Birdeye);
        case DetectionModel::M_Cured_Material:
            return paintDefectReport(size, DetectionColors::M_Cured_Material, true);
        case DetectionModel::O_Other_Defect:
            return paintEmptyPixmap(size);
        case DetectionModel::P_Fuzzball_Whorl:
            return paintDefectReport(size, DetectionColors::P_Fuzzball_Whorl);
        case DetectionModel::R1_Resin_Starved:
            return paintDefectReport(size, DetectionColors::R1_Resin_Starved, true);
        case DetectionModel::R2_Resin_Rich:
            return paintDefectReport(size, DetectionColors::R2_Resin_Rich, true);
        case DetectionModel::S_Mark_or_Scratch:
            return paintDefectReport(size, DetectionColors::S_Mark_or_Scratch, true);
        case DetectionModel::T_Fibre_Fault:
            return paintDefectReport(size, DetectionColors::T_Fibre_Fault);
        case DetectionModel::Mini_Gap:
            return paintIsraDefect(size, DetectionColors::Mini_Gap);
        case DetectionModel::Not_A_Defect:
            return paintIsraDefect(size, DetectionColors::Not_A_Defect);
        case DetectionModel::Dry_Fibre_Splice:
            return paintIsraDefect(size, DetectionColors::Dry_Fibre_Splice);
        case DetectionModel::Mini_Fibre_Fault:
            return paintIsraDefect(size, DetectionColors::Mini_Fibre_Fault);
        case DetectionModel::Mini_Unsoluble:
            return paintIsraDefect(size, DetectionColors::Mini_Unsoluble);
        case DetectionModel::Mini_Resin_Starved:
            return paintIsraDefect(size, DetectionColors::Mini_Resin_Starved);
        case DetectionModel::Mini_Fuzzball:
            return paintIsraDefect(size, DetectionColors::Mini_Fuzzball);
        default:
            return paintEmptyPixmap(size);
    }
    // unreachable: return QPixmap()
}

QPixmap DefectIconPainter::paintDefectReport(const QSize &size, const QColor &color, bool cutoutCircle)
{
    auto defectIcon = paintEmptyPixmap(size);
    defectIcon.fill(Qt::transparent);
    QPainter p(&defectIcon);
    p.setRenderHint(QPainter::Antialiasing);

    QPen pen(Qt::black);
    pen.setWidthF(1.5);
    QBrush brush(color);

    static const qreal OUTER_RADIUS = 12;
    static const qreal INNER_RADIUS = 5.5;
    static const qreal MARGINS = 2;
    QPainterPath path;
    // outer circle
    path.addEllipse(QRectF(0.0, 0.0, OUTER_RADIUS * 2.0, OUTER_RADIUS * 2.0).adjusted(MARGINS, MARGINS, -MARGINS, -MARGINS));
    // inner circle
    if (cutoutCircle) {
        QRectF innerRect(0.0, 0.0, INNER_RADIUS * 2.0, INNER_RADIUS * 2.0);
        innerRect.translate(OUTER_RADIUS - INNER_RADIUS, OUTER_RADIUS - INNER_RADIUS);
        path.addEllipse(innerRect);
    }

    p.setPen(pen);
    p.setBrush(brush);
    p.drawPath(path);

    return defectIcon;
}

QPixmap DefectIconPainter::paintIsraDefect(const QSize &size, const QColor &color)
{
    auto defectIcon = paintEmptyPixmap(size);
    defectIcon.fill(Qt::transparent);
    QPainter p(&defectIcon);
    p.setRenderHint(QPainter::Antialiasing);

    auto font = p.font();
    font.setPointSize(20);

    QPen pen(Qt::black);
    pen.setWidthF(1.5);
    QBrush brush(color);

    // use path for outlined text
    QPainterPath path;
    path.addText(0, size.height(), font, DetectionColors::Default_Defect_Symbol());

    p.setPen(pen);
    p.setBrush(brush);
    p.drawPath(path);

    //p.drawText(boundingRect, Qt::AlignCenter, DetectionColors::Default_Defect_Symbol());
    return defectIcon;
}

QPixmap DefectIconPainter::paintEmptyPixmap(const QSize &size)
{
    auto dummyIcon = QPixmap(size);
    dummyIcon.fill(Qt::transparent);
    return dummyIcon;
}
