#ifndef DEFECT_ICON_PAINTER_H
#define DEFECT_ICON_PAINTER_H

#include <QSize>
#include <QPixmap>
#include <QColor>

#include "detectionmodel.h"

/**
 * @class DefectIconPainter
 * @brief Creates QPixmaps for the different defect failure classes
 *
 * Keep this in sync with the implementation in RollScene
 * ToDo: Unify this implementation with the implementation in RollScene
 *       (the implementation in RollScene uses QGraphicsItems, the following
 *       implementaiton is QPainter based)
 */
class DefectIconPainter
{
public:
    explicit DefectIconPainter() = delete;
    ~DefectIconPainter() = default;

    static QPixmap paintDefectItem(DetectionModel::FailureType failureType,const QSize& size);

    static QPixmap paintDefectReport(const QSize& size, const QColor& color, bool cutoutCircle = false);
    static QPixmap paintIsraDefect(const QSize& size, const QColor& color);
    static QPixmap paintEmptyPixmap(const QSize& size);

};

#endif // DEFECT_ICON_PAINTER_H
