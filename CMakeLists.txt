cmake_minimum_required(VERSION 3.23)
project(HVIS VERSION 0.1 LANGUAGES CXX)

set(CMAKE_CXX_STANDARD 20)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Enable warnings and treat warnings as errors
if (MSVC)
    add_compile_options(/W4 /WX)
else()
    add_compile_options(-Wall -Wextra -Wpedantic -Werror)
endif()

# Set consistent output directories
set(CMAKE_RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin)
set(CMAKE_LIBRARY_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib)
set(CMAKE_ARCHIVE_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib)

# Include Conan generated files
if (EXISTS "${CMAKE_BINARY_DIR}/conan_toolchain.cmake")
    message(STATUS "Including conan toolchain")
    include("${CMAKE_BINARY_DIR}/conan_toolchain.cmake")
endif()

# Find spdlog package
find_package(spdlog REQUIRED)
message(STATUS "Found spdlog: ${spdlog_VERSION}")

# Find GTest package
find_package(GTest REQUIRED)
message(STATUS "Found GTest: ${GTest_VERSION}")

# Include the Qt configuration
include(${CMAKE_SOURCE_DIR}/cmake/QtConfig.cmake)

# Helper to add module targets quickly
function(add_module_target target_name)
    add_library(${name} OBJECT)
    target_sources(${name} PRIVATE ${ARGN})
    target_include_directories(${name} PUBLIC
        $<BUILD_INTERFACE:${CMAKE_SOURCE_DIR}/include>)
endfunction()

# Build each part
# file(GLOB_RECURSE MODULES_DIRS RELATIVE ${CMAKE_CURRENT_SOURCE_DIR} src/*)
# foreach(DIR ${MODULES_DIRS})
#     if(IS_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}/${DIR})
#         message(STATUS "Adding module: ${DIR}")
#         add_subdirectory(${CMAKE_CURRENT_SOURCE_DIR}/${DIR})
#     endif()
# endforeach()

# Add all source modules
add_subdirectory(src)

# Final executable
add_executable(HVIS main.cpp)
target_link_libraries(HVIS
    PRIVATE
        Manager
        spdlog::spdlog
)