#ifndef FILTER_LIST_MODEL
#define FILTER_LIST_MODEL

#include <QObject>
#include <QAbstractListModel>
#include <QList>

#include "detectionmodel.h"

/**
 * @class FilterListModel
 * @brief Model to store detection filters
 */
class FilterListModel : public QAbstractListModel
{
    Q_OBJECT

public:
    /**
     * @brief Extra roles
     */
    enum FilterListRoles {
        RawFailureClassValue = Qt::UserRole + 1
    };
    Q_ENUM(FilterListRoles)

    /**
     * @brief Constructor
     */
    explicit FilterListModel(QObject *parent = nullptr);

    /**
     * @brief Destructor
     */
    ~FilterListModel() = default;


    /**
     * @brief Returns if the given failure type is filtered by this model
     *
     * @param DetectionModel::FailureType failureType the failure type to check
     * @return true, if the failure type should be filtered, else otherwise
     */
    bool isFailureClassFiltered(DetectionModel::FailureType failureType) const;

    /**
     * @brief the number of available filters
     *        Keep in sync with DetectionModel::FailureType
     *
     * Overrides QAbstractItemModel::rowCount()
     */
    int rowCount(const QModelIndex &parent = QModelIndex()) const override;

    /**
     * @brief returns model data
     *
     * Overrides QAbstractItemModel::data()
     */
    QVariant data(const QModelIndex &index, int role = Qt::DisplayRole) const override;

    /**
     * @brief Enables filter selection by clicking on the checkbox
     *
     * Overrides QAbstractItemModel::setData()
     */
    bool setData(const QModelIndex &index, const QVariant &value, int role = Qt::EditRole) override;

    /**
     * @brief returns the item flags for this model
     *
     * Overrides QAbstractItemModel::flags()
     */
    Qt::ItemFlags flags(const QModelIndex &index) const override;


private:
    void initializeModelFromFailureTypes();

    QList<std::pair<DetectionModel::FailureType, bool>> m_filterList;

};
#endif // FILTER_LIST_MODEL
