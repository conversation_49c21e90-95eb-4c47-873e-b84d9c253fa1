#include "defectitem.h"
#include "ui_defectitem.h"

#include <QSize>
#include <QRectF>
#include <QLabel>
#include <QListView>

#include "defecticonpainter.h"

#include "spdlog/sinks/stdout_color_sinks.h"

using namespace Qt::StringLiterals;

static const int DEFECT_IMAGE_MARGINS = 28;

DefectItem::DefectItem(QWidget *parent)
    : QWidget(parent)
    , ui(new Ui::DefectItem)
{
    m_logger = spdlog::get("UserInterfaceApplication");
    if (!m_logger) {
        m_logger = spdlog::stdout_color_mt("UserInterfaceApplication");
        m_logger->set_level(spdlog::level::debug);
    }
    ui->setupUi(this);
    initializeUI();
}

DefectItem::~DefectItem()
{
    delete ui;
}

void DefectItem::setDetectionNumber(int detectionNumber)
{
    m_detectionNumber = detectionNumber;
    // model is transposed, so detections are stored column wise
    ui->lastDefectDetails->setModelColumn(detectionNumber);
}

int DefectItem::detectionNumber() const
{
    return m_detectionNumber;
}

void DefectItem::setDetectionId(const QString& detectionId)
{
    m_detectionId = detectionId;
}

QString DefectItem::detectionId() const
{
    return m_detectionId;
}

void DefectItem::setFailureType(DetectionModel::FailureType failureType)
{
    m_failureType = failureType;
    auto size = ui->failureTypeLabel->size();
    ui->failureTypeLabel->setPixmap(DefectIconPainter::paintDefectItem(m_failureType, size));
}

DetectionModel::FailureType DefectItem::failureType() const
{
    return m_failureType;
}

void DefectItem::setDetectionImage(const QImage &detectionImage)
{
    m_detectionImage = detectionImage;
    QPixmap defectPixmap = QPixmap::fromImage(detectionImage);
    const auto maxLabelSize = ui->lastDefectImageContainer->size().shrunkBy( { 0, DEFECT_IMAGE_MARGINS,
                                                                               0, DEFECT_IMAGE_MARGINS });
    if (defectPixmap.height() > maxLabelSize.height()) {
        defectPixmap = defectPixmap.scaled(maxLabelSize, Qt::KeepAspectRatio, Qt::SmoothTransformation);
    }
    ui->defectImageLabel->setPixmap(defectPixmap);
}

QImage DefectItem::detectionImage() const
{
    return m_detectionImage;
}

void DefectItem::setDetectionInfoModel(QAbstractItemModel *detectionInfoModel)
{
    auto detailsWidget = ui->lastDefectDetails;
    m_detectionInfoModel = detectionInfoModel;
    detailsWidget->setModel(m_detectionInfoModel);
    // row and columns are swapped due to QTransposeProxyModel
    detailsWidget->setRowHidden(DetectionModel::Id, true);
    detailsWidget->setRowHidden(DetectionModel::FailureClass, true);
    detailsWidget->setRowHidden(DetectionModel::DetectionImage, true);
}

QAbstractItemModel *DefectItem::detectionInfoModel() const
{
    return m_detectionInfoModel.data();
}

void DefectItem::initializeUI()
{

}

