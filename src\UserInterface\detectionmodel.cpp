#include "detectionmodel.h"

#include <QString>
#include <QUuid>
#include <QTextStream>

using namespace Qt::StringLiterals;

DetectionModel::DetectionModel(QObject *parent)
    : QAbstractTableModel(parent)
{

}

QString DetectionModel::failureTypeToString(FailureType failureType)
{
    switch (failureType) {
        case DetectionModel::Unknown:
            return QObject::tr("Unknown");
        case DetectionModel::C_Crease:
            return QObject::tr("C Crease");
        case DetectionModel::E_Edge_Defect:
            return QObject::tr("E Edge Defect");
        case DetectionModel::F_Foreign_Body:
            return QObject::tr("F Foreign Body");
        case DetectionModel::J1_Prepreg_Splice:
            return QObject::tr("J1 Prepreg Splice");
        case DetectionModel::J2_Paper_Splice:
            return QObject::tr("J2 Paper Splice");
        case DetectionModel::K_Gap_Birdeye:
            return QObject::tr("K Gap Birdeye");
        case DetectionModel::M_Cured_Material:
            return QObject::tr("M Cured Material");
        case DetectionModel::O_Other_Defect:
            return QObject::tr("O Other Defect");
        case DetectionModel::P_Fuzzball_Whorl:
            return QObject::tr("P Fuzzball Whorl");
        case DetectionModel::R1_Resin_Starved:
            return QObject::tr("R1 Resin Starved");
        case DetectionModel::R2_Resin_Rich:
            return QObject::tr("R2 Resin Rich");
        case DetectionModel::S_Mark_or_Scratch:
            return QObject::tr("S Mark or Scratch");
        case DetectionModel::T_Fibre_Fault:
            return QObject::tr("T Fibre Fault");
        case DetectionModel::Mini_Gap:
            return QObject::tr("Mini Gap");
        case DetectionModel::Not_A_Defect:
            return QObject::tr("Not A Defect");
        case DetectionModel::Dry_Fibre_Splice:
            return QObject::tr("Dry Fibre Splice");
        case DetectionModel::Mini_Fibre_Fault:
            return QObject::tr("Mini Fibre Fault");
        case DetectionModel::Mini_Unsoluble:
            return QObject::tr("Mini Unsoluble");
        case DetectionModel::Mini_Resin_Starved:
            return QObject::tr("Mini Resin Starved");
        case DetectionModel::Mini_Fuzzball:
            return QObject::tr("Mini Fuzzball");
        case DetectionModel::Development_Test_1:
            return QObject::tr("Development Test: 1");
        case DetectionModel::Development_Test_2:
            return QObject::tr("Development Test: 2");
        default:
            return QString();
    }
}

int DetectionModel::rowCount(const QModelIndex &parent) const
{
    Q_UNUSED(parent)
    return m_detections.count();

}

int DetectionModel::columnCount(const QModelIndex &parent) const
{
    Q_UNUSED(parent)
    return int(DetectionColumnRoles::AffectedSubRolls) + 1;
}

QVariant DetectionModel::data(const QModelIndex &index, int role) const
{
    if (!checkIndex(index)) {
        return QVariant();
    }

    Q_ASSERT(m_detections.count() == m_detectionIds.count());

    if (role == Qt::DisplayRole) {
        const auto &item = m_detections.at(index.row());
        switch(index.column()) {
            case DetectionColumnRoles::Id:
                return m_detectionIds.at(index.row());
            case DetectionColumnRoles::FailureClass:
                return item.failure;
            case DetectionColumnRoles::FailureString:
                return DetectionModel::failureTypeToString(item.failure);
            case DetectionColumnRoles::BoundingRect:
                return item.boundingRect;
            case DetectionColumnRoles::ConfidenceScore:
                return item.confidenceScore;
            case DetectionColumnRoles::DetectionImage:
                return item.detectionImage;
            case DetectionColumnRoles::AffectedSubRolls:
            {
                QString str;
                QTextStream stream(&str);
                int cnt = 0;
                for (auto affectedSubRoll : item.affectedSubRolls) {
                    stream << QString::number(affectedSubRoll);
                    if (cnt < item.affectedSubRolls.count() - 1) {
                        stream << u" >> "_s;
                    }
                    cnt++;
                }
                return stream.readAll();
            }
            default: return QVariant();
        }
    }

    return QVariant();
}

void DetectionModel::reset()
{
    beginResetModel();
    m_detections.clear();
    endResetModel();
}

void DetectionModel::addDetection(const Detection& detection)
{
    auto newPosition = m_detections.count();
    beginInsertRows(QModelIndex(), newPosition, newPosition);
    m_detections.push_back(detection);
    auto uuid = QUuid::createUuid();
    m_detectionIds.push_back(uuid.toString());
    endInsertRows();
}

bool  DetectionModel::updateDetection(int idx, const Detection& detection)
{
    if (idx < 0 || idx >= m_detections.count()) {
        return false;
    }
    auto left = index(idx, 0);
    auto right = index(idx, columnCount() - 1);
    m_detections[idx] = detection;
    emit dataChanged(left, right, { Qt::DisplayRole });
    return true;
}

bool  DetectionModel::updateAffectedSubRolls(int idx,const QList<int> &affectedSubRolls)
{
    if (idx < 0 || idx >= m_detections.count()) {
        return false;
    }

    auto left = index(idx, DetectionColumnRoles::AffectedSubRolls);
    auto right = index(idx, DetectionColumnRoles::AffectedSubRolls);
    m_detections[idx].affectedSubRolls = affectedSubRolls;
    emit dataChanged(left, right, { Qt::DisplayRole });
    return true;
}
