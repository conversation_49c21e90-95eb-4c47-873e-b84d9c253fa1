<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>DefectItem</class>
 <widget class="QWidget" name="DefectItem">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>622</width>
    <height>564</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout">
     <item>
      <spacer name="horizontalSpacer">
       <property name="orientation">
        <enum>Qt::Orientation::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
     <item>
      <widget class="QLabel" name="failureTypeLabel">
       <property name="minimumSize">
        <size>
         <width>24</width>
         <height>24</height>
        </size>
       </property>
       <property name="maximumSize">
        <size>
         <width>24</width>
         <height>24</height>
        </size>
       </property>
       <property name="baseSize">
        <size>
         <width>24</width>
         <height>24</height>
        </size>
       </property>
       <property name="text">
        <string>IconLabel</string>
       </property>
      </widget>
     </item>
    </layout>
   </item>
   <item>
    <widget class="QWidget" name="lastDefectImageContainer" native="true">
     <property name="minimumSize">
      <size>
       <width>0</width>
       <height>200</height>
      </size>
     </property>
     <layout class="QHBoxLayout" name="horizontalLayout_2">
      <item>
       <spacer name="horizontalSpacer_2">
        <property name="orientation">
         <enum>Qt::Orientation::Horizontal</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>40</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
      <item>
       <widget class="QLabel" name="defectImageLabel">
        <property name="text">
         <string>ImageLabel</string>
        </property>
        <property name="alignment">
         <set>Qt::AlignmentFlag::AlignLeading|Qt::AlignmentFlag::AlignLeft|Qt::AlignmentFlag::AlignTop</set>
        </property>
       </widget>
      </item>
      <item>
       <spacer name="horizontalSpacer_3">
        <property name="orientation">
         <enum>Qt::Orientation::Horizontal</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>40</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <widget class="QListView" name="lastDefectDetails">
     <property name="minimumSize">
      <size>
       <width>0</width>
       <height>200</height>
      </size>
     </property>
    </widget>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
