#ifndef DEFECTITEM_H
#define DEFECTITEM_H

#include <QWidget>
#include <QPointer>
#include <QImage>
#include <QAbstractItemModel>

#include "detectionmodel.h"

#include "spdlog/spdlog.h"

QT_BEGIN_NAMESPACE
namespace Ui {
class DefectItem;
}
QT_END_NAMESPACE

class DefectItem : public QWidget
{
    Q_OBJECT

public:
    explicit DefectItem(QWidget *parent = nullptr);
    ~DefectItem();

    void setDetectionNumber(int detectionNumber);
    int detectionNumber() const;

    void setDetectionId(const QString& detectionId);
    QString detectionId() const;

    void setFailureType(DetectionModel::FailureType  failureType);
    DetectionModel::FailureType failureType() const;

    void setDetectionImage(const QImage& detectionImage);
    QImage detectionImage() const;

    void setDetectionInfoModel(QAbstractItemModel* detectionModel);
    QAbstractItemModel* detectionInfoModel() const;


private:
    void initializeUI();

    int m_detectionNumber { - 1 };
    QString m_detectionId;

    DetectionModel::FailureType m_failureType;
    QImage m_detectionImage;
    QPointer<QAbstractItemModel> m_detectionInfoModel;

    Ui::DefectItem *ui;
    std::shared_ptr<spdlog::logger> m_logger;

};
#endif // DEFECTITEM_H
