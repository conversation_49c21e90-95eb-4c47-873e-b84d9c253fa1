#include "liveviewwidget.h"
#include "ui_liveviewwidget.h"

#include "scenemanager.h"

#include "spdlog/sinks/stdout_color_sinks.h"

#include <QSize>
#include <QRectF>

LiveViewWidget::LiveViewWidget(QWidget *parent)
    : QWidget(parent)
    , ui(new Ui::LiveViewWidget)
{
    m_logger = spdlog::get("UserInterfaceApplication");
    if (!m_logger) {
        m_logger = spdlog::stdout_color_mt("UserInterfaceApplication");
        m_logger->set_level(spdlog::level::debug);
    }
    ui->setupUi(this);
    initializeUI();
}

LiveViewWidget::~LiveViewWidget()
{
    delete ui;
}

void LiveViewWidget::initializeUI()
{
    const qreal detectionViewHeight = height() * 0.4;
    ui->topCameraRecentDetections->setMinimumHeight(detectionViewHeight);
    ui->bottomCameraRecentDetections->setMinimumHeight(detectionViewHeight);
}

void LiveViewWidget::setSceneManager(SceneManager *sceneManager)
{
    if (m_sceneManager.isNull()) {
        m_sceneManager = sceneManager;
        ui->topCameraView->initialize(SceneManager::TopCameraView, m_sceneManager);
        ui->bottomCameraView->initialize(SceneManager::BottomCameraView, m_sceneManager);
        ui->topCameraRecentDetections->initialize(SceneManager::TopCameraView, m_sceneManager);
        ui->bottomCameraRecentDetections->initialize(SceneManager::BottomCameraView, m_sceneManager);
    } else {
        m_logger->warn("Scene Manager already active");
    }
}

SceneManager* LiveViewWidget::sceneManager() const
{
    return m_sceneManager;
}
