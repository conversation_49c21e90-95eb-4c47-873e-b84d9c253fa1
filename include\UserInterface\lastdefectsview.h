#ifndef LASTDEFECTVIEW_H
#define LASTDEFECTVIEW_H

#include <QListWidget>
#include <QWidget>
#include <QPointer>
#include <QSize>

#include "spdlog/spdlog.h"

#include "scenemanager.h"

QT_BEGIN_NAMESPACE
namespace Ui {
class LastDefectsView;
}
QT_END_NAMESPACE

class QShowEvent;
class QResizeEvent;
class QTransposeProxyModel;

class LastDefectsView : public QListWidget
{
    Q_OBJECT

public:
    explicit LastDefectsView(QWidget *parent = nullptr);
    ~LastDefectsView();

    void initialize(SceneManager::CameraPosition position, SceneManager *sceneManager);

    void showEvent(QShowEvent *event) override;
    void resizeEvent(QResizeEvent *event) override;

private:
    void initializeUI();
    void resetGridSize();
    void addDetectionToView(int row);

    Ui::LastDefectsView *ui;
    QSize m_gridSize;
    SceneManager::CameraPosition m_cameraPosition { SceneManager::UnknownCameraPosition };
    QPointer<SceneManager> m_sceneManager;
    QTransposeProxyModel* m_listModel { nullptr };

    std::shared_ptr<spdlog::logger> m_logger;

};
#endif // LASTDEFECTVIEW_H
