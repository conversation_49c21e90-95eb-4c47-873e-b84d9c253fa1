#include "PrepregManager.h"
#include "ImageAcquisition/EuresysFrameGrabber.h"
// #include "ImageOperations.h"

#include <iostream>

#include "spdlog/spdlog.h"
#include "spdlog/sinks/stdout_color_sinks.h"


PrepregManager::PrepregManager()
    : m_frameGrabber(std::make_unique<HVIS::EuresysFrameGrabber>())
{
    m_logger = spdlog::get("PrepregManager");
    if (!m_logger) {
        m_logger = spdlog::stdout_color_mt("PrepregManager");
        m_logger->set_level(spdlog::level::debug);
        m_logger->debug("PrepregManager logger created");
    } else
    {
        m_logger->debug("PrepregManager logger already created");
    }
    
    m_logger->debug("Calling PrepregManager constructor");

    // m_app = new QApplication(argc, argv);
    // m_uiManager = std::make_unique<MainWindow>();

    // QObject::connect(m_uiManager.get(), SIGNAL(actionTriggered()), this, SLOT(doSomethingOnTheInterface()));
}

PrepregManager::~PrepregManager() 
{
    // delete m_app;
}

void PrepregManager::setupLoggers(std::vector<spdlog::sink_ptr> t_sinks)
{
    auto prepreg_logger = std::make_shared<spdlog::logger>("PrepregManager", t_sinks.begin(), t_sinks.end());
    spdlog::register_logger(prepreg_logger);
    prepreg_logger->set_level(spdlog::level::debug);
}

int PrepregManager::show()
{
    // m_uiManager->show();
    // return m_app->exec();
    return 0;
}

void PrepregManager::runImageAcquisition()
{
    m_frameGrabber->initialize(HVIS::EuresysFrameGrabber::EuresysProducer::Playlink);
    m_frameGrabber->selectCamera(1);
    m_frameGrabber->setCallback([](void* bufferData, size_t width, size_t height, size_t size, size_t frameId) {
        (void)bufferData;
        std::cout << "Frame received: ID=" << frameId << ", Size=" << size << ", Resolution=" << width << "x" << height << std::endl;

        std::cout << "1 frame received" << std::endl;
        // splitImageCUDA(static_cast<uint8_t*>(bufferData), width, height, 3, 16, width * 3, 640, 640);
        exit(0);
    });
    // m_frameGrabber->allocBuffers(10);
    m_frameGrabber->allocateAndAnnounceGPUBuffers(10);
    m_frameGrabber->startAcquisition();

    std::this_thread::sleep_for(std::chrono::seconds(5));
    m_frameGrabber->stopAcquisition();
}

void PrepregManager::doSomethingOnTheInterface()
{
    std::cout << "Action triggered from the interface!" << std::endl;
    this->runImageAcquisition();
}

