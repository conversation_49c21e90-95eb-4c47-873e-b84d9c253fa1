#include "filterlistmodel.h"

#include <QString>

#include <defecticonpainter.h>

using namespace Qt::StringLiterals;

static const int ICON_SIZE = 24;

FilterListModel::FilterListModel(QObject *parent)
    : QAbstractListModel(parent)
{
    initializeModelFromFailureTypes();
}

bool FilterListModel::isFailureClassFiltered(DetectionModel::FailureType failureType) const
{
    auto it = std::find_if(m_filterList.begin(),
                           m_filterList.end(),
                           [failureType](std::pair<DetectionModel::FailureType, bool> entry) {
        return failureType == entry.first;
    });
    if (m_filterList.end() != it) {
        auto visible = it->second;
        return !visible;
    }
    return false;
}

int FilterListModel::rowCount(const QModelIndex &parent) const
{
    Q_UNUSED(parent);
    return int(DetectionModel::Mini_Fuzzball) + 1;
}

QVariant FilterListModel::data(const QModelIndex &index, int role) const
{
    if (!checkIndex(index)) {
        return QVariant();
    }

    const auto &item = m_filterList.at(index.row());
    if (role == Qt::DisplayRole) {
        return DetectionModel::failureTypeToString(item.first);
    } else if (role == Qt::CheckStateRole)  {
        return item.second ? Qt::Checked : Qt::Unchecked;
    } else if (role == Qt::DecorationRole) {
        return DefectIconPainter::paintDefectItem(item.first, QSize(ICON_SIZE, ICON_SIZE));
    } else if (role == FilterListModel::RawFailureClassValue) {
        return item.first;
    }

    return QVariant();

}

bool FilterListModel::setData(const QModelIndex &index, const QVariant &value, int role)
{
    if (role == Qt::CheckStateRole) {
        auto checkedState = value.toBool();
        auto &item = m_filterList[index.row()];
        item.second = checkedState;
        emit dataChanged(index, index, { Qt::CheckStateRole });
        return true;
    }
    return QAbstractListModel::setData(index, value, role);
}

Qt::ItemFlags FilterListModel::flags(const QModelIndex &index) const
{
    auto currentFlags = QAbstractListModel::flags(index);
    return currentFlags | Qt::ItemIsUserCheckable;
}

void FilterListModel::initializeModelFromFailureTypes()
{
    beginResetModel();
    const auto lastFailureTypeValue =  int(DetectionModel::Mini_Fuzzball) + 1;
    m_filterList.clear();
    for (auto i = 0; i <lastFailureTypeValue; i++) {
        auto failureType = static_cast<DetectionModel::FailureType>(i);
        m_filterList.push_back(std::make_pair(failureType, true));
    }
    endResetModel();
}
