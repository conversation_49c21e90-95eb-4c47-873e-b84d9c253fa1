# ------------- Module (UserInterface) -------------
add_library(UserInterface STATIC)

# List all the sources in this directory
file(GLOB_RECURSE UI_SOURCES CONFIGURE_DEPENDS "*.cpp")

target_sources(UserInterface 
    PRIVATE
        ${UI_SOURCES}
)

set_target_properties(UserInterface 
    PROPERTIES 
        AUTOUIC_SEARCH_PATHS ${CMAKE_SOURCE_DIR}/ui
        WINDOWS_EXPORT_ALL_SYMBOLS ON
)

target_include_directories(UserInterface 
    PUBLIC
        $<BUILD_INTERFACE:${CMAKE_SOURCE_DIR}/include>
    PRIVATE
        $<BUILD_INTERFACE:${CMAKE_SOURCE_DIR}/include/UserInterface>
)

target_link_libraries(UserInterface
    PUBLIC
        Qt6::Core
        Qt6::Widgets
        spdlog::spdlog
)