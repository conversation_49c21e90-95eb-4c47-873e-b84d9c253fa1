#include "detectionsimulator.h"

#include "detectionmodel.h"

#include <QObject>
#include <QDebug>
#include <QDir>
#include <QPointF>

using namespace Qt::StringLiterals;

DetectionSimulator::DetectionSimulator(const SimulatorConfiguration &config, SceneManager *sceneManager)
    : m_sceneManager(sceneManager)
{
    m_speedInCMPerSecond = (config.speedInMeterPerMinute / 60.0) * 100.0;
    m_defectProbability = config.defectProbability;

    if (config.useDeterministicRandomGenerator) {
        m_randomGenerator = QRandomGenerator(config.seedValue);
    } else {
        m_randomGenerator = QRandomGenerator(QRandomGenerator::global()->generate());
    }

    // DetectionSimulator is not a QObject, so use m_timer as q context object here
    // when the detection simulator is destroyed, the QTimer is also destroyed,
    // and the connection gets inactive
    QObject::connect(&m_timer, &QTimer::timeout, &m_timer, [this]() {
        this->step();
    });
}

void DetectionSimulator::start()
{
    pickRandomImage(m_randomGenerator);
    m_timer.start(1000);
}

void DetectionSimulator::stop()
{
    m_timer.stop();
}

QImage DetectionSimulator::pickRandomImage(QRandomGenerator& rnd) const
{
    const auto resourcePath = u":/assets/exampledetections"_s;
    QDir resourceDir(resourcePath);

    auto entries = resourceDir.entryList(QDir::Files | QDir::NoDotAndDotDot);
    auto randomSelected = rnd.bounded(entries.count());
    auto imageFilePath = resourceDir.absoluteFilePath(entries.at(randomSelected));
    return QImage(imageFilePath);
}

void DetectionSimulator::step()
{
    if (!m_sceneManager.isNull()) {

        const std::vector<SceneManager::CameraPosition> positions = {SceneManager::TopCameraView,
                                                                     SceneManager::BottomCameraView };

        for (const auto position : positions) {
            if (m_randomGenerator.generateDouble() < m_defectProbability) {
                qreal halfDefectSize = 60.0;

                auto detectionModel = m_sceneManager->detectionModel(position);
                auto viewport = m_sceneManager->cameraViewport(position).toAlignedRect();
                auto jitter = m_randomGenerator.generateDouble() * 10.0 - 5.0;
                auto defectPosition = QPointF(m_randomGenerator.bounded(
                    viewport.x() + static_cast<int>(m_sceneManager->leftBorderWidth()),
                    viewport.right() - static_cast<int>(m_sceneManager->rightBorderWidth())),
                    viewport.bottom() - 50 - jitter
                );

                QRectF bbox;
                bbox.setCoords(defectPosition.x() - halfDefectSize, defectPosition.y() - halfDefectSize,
                               defectPosition.x() + halfDefectSize, defectPosition.y() + halfDefectSize);

                Q_ASSERT(detectionModel);
                auto dummyImage = pickRandomImage(m_randomGenerator);
                DetectionModel::Detection detection;
                detection.failure = static_cast<DetectionModel::FailureType>(
                    m_randomGenerator.bounded(static_cast<int>(DetectionModel::Development_Test_2 + 1)));
                detection.boundingRect = bbox;
                detection.confidenceScore = 0.75;
                detection.detectionImage = dummyImage;
                detectionModel->addDetection(detection);
            }
        }

        m_sceneManager->moveRoll(m_speedInCMPerSecond);

    }
}
